# Pi3 Training Implementation

This directory contains a complete training implementation for Pi3 (π³) models, designed for fine-tuning tasks with permutation-equivariant visual geometry learning.

## 🌟 Features

- **Permutation-Equivariant Training**: Implements Pi3's core permutation-equivariant design
- **Scale-Invariant Learning**: Supports scale-invariant training procedures
- **Confidence-Based Learning**: Incorporates uncertainty estimation and confidence weighting
- **Multi-GPU Support**: Distributed training with automatic scaling
- **Comprehensive Logging**: TensorBoard integration with visual monitoring
- **Flexible Configuration**: Hydra-based configuration management

## 📁 Directory Structure

```
Pi3/training/
├── config/                     # Configuration files
│   ├── pi3_default.yaml       # Main training configuration
│   └── pi3_dataset.yaml       # Dataset-specific settings
├── data/                       # Data loading and processing
│   ├── pi3_dataloader.py      # Pi3-specific data loader
│   └── pi3_augmentation.py    # Augmentation pipeline
├── loss.py                     # Pi3 loss functions
├── pi3_trainer.py             # Main training logic
├── train_pi3.py               # Training script
├── launch_pi3_training.py     # Simplified launcher
├── test_pi3_training.py       # Test suite
└── README.md                  # This file
```

## 🚀 Quick Start

### 1. Environment Setup

Ensure you have the required dependencies installed:

```bash
# Install Pi3 requirements
cd Pi3
pip install -r requirements.txt

# Install additional training dependencies
pip install hydra-core omegaconf tensorboard
```

### 2. Simple Training Launch

Use the simplified launcher for quick training:

```bash
# Single GPU training with default settings
python launch_pi3_training.py

# Multi-GPU training
python launch_pi3_training.py --gpus 4

# Custom configuration
python launch_pi3_training.py --config config/custom_config.yaml --gpus 2

# Resume from checkpoint
python launch_pi3_training.py --resume checkpoints/latest.pt
```

### 3. Advanced Training

For more control, use the main training script:

```bash
# Single GPU training
python train_pi3.py --config config/pi3_default.yaml

# Multi-GPU distributed training
python -m torch.distributed.launch --nproc_per_node=4 train_pi3.py \
    --config config/pi3_default.yaml --distributed

# Validation only
python train_pi3.py --config config/pi3_default.yaml --validate_only

# Debug mode
python train_pi3.py --config config/pi3_default.yaml --debug
```

## ⚙️ Configuration

### Main Configuration (`config/pi3_default.yaml`)

Key configuration sections:

```yaml
# Experiment settings
exp_name: pi3_exp001
img_size: 518
max_epochs: 50
seed_value: 42

# Model configuration
model:
  _target_: pi3.models.pi3.Pi3
  pos_type: rope100
  decoder_size: large

# Loss configuration
loss:
  _target_: loss.Pi3MultitaskLoss
  points:
    weight: 1.0
    scale_invariant: True
  confidence:
    weight: 0.5
  camera:
    weight: 2.0
    loss_type: "geodesic"

# Optimizer settings
optim:
  optimizer:
    _target_: torch.optim.AdamW
    lr: 3e-5
    weight_decay: 0.01
```

### Dataset Configuration (`config/pi3_dataset.yaml`)

Configure data loading and augmentation:

```yaml
# Augmentation settings
augmentation:
  random_view_permutation: True
  permutation_probability: 0.8
  random_scale: True
  scale_range: [0.5, 2.0]

# Sequence sampling
sequence_sampling:
  min_views: 3
  max_views: 8
  sampling_strategy: "uniform"
```

## 🔧 Key Components

### 1. Pi3MultitaskLoss (`loss.py`)

Implements Pi3-specific loss functions:

- **Point Reconstruction Loss**: Scale-invariant 3D point loss
- **Confidence Loss**: Uncertainty-aware confidence weighting
- **Camera Pose Loss**: Geodesic distance for rotation matrices
- **Permutation Consistency Loss**: Ensures equivariance to view ordering
- **Scale Invariance Loss**: Maintains scale-invariant representations

### 2. Pi3Trainer (`pi3_trainer.py`)

Extends VGGT's trainer with Pi3-specific features:

- Permutation-aware data processing
- Scale normalization
- Confidence map generation
- Pi3-specific visualization logging

### 3. Pi3DynamicTorchDataset (`data/pi3_dataloader.py`)

Specialized data loader supporting:

- Variable sequence lengths
- Permutation-equivariant batching
- Scale augmentation
- Confidence weighting

### 4. Pi3AugmentationPipeline (`data/pi3_augmentation.py`)

Comprehensive augmentation pipeline:

- Geometric augmentations (crop, scale)
- Photometric augmentations (color jitter)
- Permutation augmentations
- Temporal augmentations

## 📊 Monitoring and Logging

### TensorBoard Visualization

Launch TensorBoard to monitor training:

```bash
tensorboard --logdir logs/pi3_exp001/tensorboard
```

Logged metrics include:
- Loss components (points, confidence, camera, permutation consistency)
- Learning rates and gradients
- Visual outputs (point clouds, confidence maps)
- Training statistics

### Log Structure

```
logs/
└── pi3_exp001/
    ├── ckpts/              # Model checkpoints
    ├── tensorboard/        # TensorBoard logs
    └── training.log        # Text logs
```

## 🧪 Testing

Run the test suite to verify implementation:

```bash
# Run all tests
python test_pi3_training.py

# Run specific test
python test_pi3_training.py --test_name TestPi3Loss.test_loss_computation

# Verbose output
python test_pi3_training.py --verbose
```

## 🔍 Key Training Parameters

### Learning Rate Schedule

- **Warmup**: Linear warmup for 10% of training
- **Main**: Cosine annealing for remaining 90%
- **Base LR**: 3e-5 (lower than VGGT due to Pi3's complexity)

### Batch Size and Accumulation

- **Max images per GPU**: 24 (reduced due to memory requirements)
- **Gradient accumulation**: 4 steps (helps with stability)
- **Effective batch size**: Scales with number of GPUs

### Loss Weights

- **Points**: 1.0 (primary geometric loss)
- **Camera**: 2.0 (important for pose accuracy)
- **Confidence**: 0.5 (uncertainty estimation)
- **Permutation Consistency**: 0.1 (equivariance regularization)
- **Scale Invariance**: 0.05 (scale robustness)

## 🚨 Common Issues and Solutions

### Memory Issues

If you encounter OOM errors:

1. Reduce `max_img_per_gpu` in config
2. Increase `accum_steps` to maintain effective batch size
3. Enable gradient checkpointing
4. Use smaller image resolution

### Convergence Issues

For training instability:

1. Reduce learning rate
2. Increase gradient clipping
3. Adjust loss weights
4. Check data quality and preprocessing

### Distributed Training Issues

For multi-GPU problems:

1. Ensure all GPUs are visible: `export CUDA_VISIBLE_DEVICES=0,1,2,3`
2. Check network connectivity between nodes
3. Verify consistent NCCL versions
4. Use appropriate backend (nccl for GPU, gloo for CPU)

## 📚 References

- **Pi3 Paper**: [π³: Scalable Permutation-Equivariant Visual Geometry Learning](https://arxiv.org/abs/2507.13347)
- **VGGT Paper**: [VGGT: Visual Geometry Grounded Transformer](https://arxiv.org/abs/2503.11651)
- **Base Implementation**: Built upon VGGT training framework

## 🤝 Contributing

When contributing to the Pi3 training code:

1. Follow the existing code structure and naming conventions
2. Add comprehensive docstrings with English comments
3. Include unit tests for new functionality
4. Update configuration files as needed
5. Test with both single and multi-GPU setups

## 📄 License

This implementation is licensed under the 2-clause BSD License, consistent with the Pi3 project license.
