# Pi3-specific dataset configuration
# Supports permutation-equivariant training with variable sequence lengths

dataset_type: "pi3_multiview"

# Data augmentation settings for Pi3
augmentation:
  # Permutation-based augmentations
  random_view_permutation: True
  permutation_probability: 0.8
  
  # Geometric augmentations
  random_crop: True
  crop_probability: 0.7
  crop_scale_range: [0.8, 1.0]
  
  # Photometric augmentations
  color_jitter: True
  color_jitter_params:
    brightness: 0.2
    contrast: 0.2
    saturation: 0.1
    hue: 0.05
  
  # Scale augmentation for scale-invariant training
  random_scale: True
  scale_range: [0.5, 2.0]
  scale_probability: 0.6

# Sequence sampling settings
sequence_sampling:
  min_views: 3
  max_views: 8
  sampling_strategy: "uniform"  # "uniform", "temporal", "spatial"
  temporal_stride_range: [1, 5]
  spatial_coverage_threshold: 0.3

# Data loading settings
data_loading:
  shuffle_sequences: True
  drop_last: True
  pin_memory: True
  persistent_workers: True
  prefetch_factor: 2

# Quality filtering
quality_filters:
  min_image_resolution: [256, 256]
  max_image_resolution: [2048, 2048]
  min_valid_pixels_ratio: 0.5
  max_motion_blur_score: 0.8
  min_texture_score: 0.3

# Camera parameters
camera_settings:
  normalize_intrinsics: True
  canonical_camera_distance: 1.0
  up_vector: [0, 1, 0]  # Y-up coordinate system
  coordinate_system: "opencv"  # "opencv" or "opengl"

# Point cloud settings
point_cloud:
  max_points_per_view: 100000
  point_sampling_strategy: "uniform"  # "uniform", "fps", "random"
  remove_outliers: True
  outlier_threshold: 3.0

# Validation settings
validation:
  fixed_sequences: True  # Use fixed sequences for reproducible validation
  sequence_ids: null     # Specific sequence IDs for validation (null = auto-select)
  evaluation_metrics:
    - "camera_pose_error"
    - "point_cloud_accuracy"
    - "depth_error"
    - "permutation_consistency"

# Dataset-specific configurations
co3d:
  categories: 
    - "apple"
    - "backpack" 
    - "banana"
    - "baseballbat"
    - "baseballglove"
    - "bench"
    - "bicycle"
    - "bottle"
    - "bowl"
    - "broccoli"
    - "cake"
    - "car"
    - "carrot"
    - "cellphone"
    - "chair"
    - "couch"
    - "cup"
    - "donut"
    - "frisbee"
    - "hotdog"
    - "hydrant"
    - "keyboard"
    - "kite"
    - "laptop"
    - "microwave"
    - "motorcycle"
    - "mouse"
    - "orange"
    - "parkingmeter"
    - "pizza"
    - "plant"
    - "stop_sign"
    - "stopsign"
    - "suitcase"
    - "teddy"
    - "toaster"
    - "toilet"
    - "teddybear"
    - "tv"
    - "umbrella"
    - "vase"
    - "wineglass"
  
  min_sequence_length: 10
  max_sequence_length: 100
  frame_sampling_rate: 2
  
  # Quality thresholds specific to CO3D
  min_viewpoint_diversity: 0.4
  max_camera_distance_ratio: 5.0
  min_overlap_ratio: 0.3

# Memory optimization
memory_optimization:
  enable_gradient_checkpointing: True
  mixed_precision_training: True
  cpu_offload_optimizer: False
  pin_memory_device: "cuda"
  
# Debugging and monitoring
debug:
  save_sample_batches: False
  sample_batch_frequency: 100
  visualize_augmentations: False
  log_data_loading_time: True
  check_data_consistency: True

# Pi3-specific data processing
pi3_data_processing:
  # Normalize images using ImageNet statistics (required for DINOv2)
  normalize_images: True
  image_mean: [0.485, 0.456, 0.406]
  image_std: [0.229, 0.224, 0.225]
  
  # Local coordinate system settings
  local_coordinate_normalization: True
  depth_range: [0.1, 100.0]
  
  # Confidence map generation
  generate_confidence_maps: True
  confidence_computation_method: "geometric"  # "geometric", "photometric", "learned"
  
  # Camera pose representation
  camera_pose_representation: "matrix"  # "matrix", "quaternion", "axis_angle"
  normalize_camera_poses: True
  
  # Point cloud processing
  point_cloud_coordinate_system: "camera"  # "camera", "world"
  apply_depth_filtering: True
  depth_filter_threshold: 0.01
