defaults:
  - pi3_dataset.yaml

exp_name: pi3_exp001
img_size: 518
num_workers: 8
seed_value: 42
accum_steps: 4    # Pi3 may need more gradient accumulation due to permutation-equivariant training
patch_size: 14
val_epoch_freq: 5
max_img_per_gpu: 24  # Reduced due to Pi3's larger memory footprint

limit_train_batches: 800
limit_val_batches: 400

data:
  # Pi3-specific data configuration with permutation-equivariant support
  train:
    _target_: data.dynamic_dataloader.Pi3DynamicTorchDataset
    num_workers: ${num_workers}
    max_img_per_gpu: ${max_img_per_gpu}
    common_config:
      img_size: ${img_size}
      patch_size: ${patch_size}
      debug: True
      repeat_batch: False
      permutation_augment: True  # Enable permutation-based data augmentation
      max_sequence_length: 8     # Maximum number of views per sequence
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        - _target_: data.datasets.co3d.Co3dDataset
          split: train
          CO3D_DIR: /defaultShare/pubdata/3D/co3d
          CO3D_ANNOTATION_DIR: /defaultShare/pubdata/3D/co3d_anno
  val:
    _target_: data.dynamic_dataloader.Pi3DynamicTorchDataset
    num_workers: ${num_workers}
    max_img_per_gpu: ${max_img_per_gpu}
    common_config:
      img_size: ${img_size}
      patch_size: ${patch_size}
      debug: True
      permutation_augment: False  # No augmentation during validation
      max_sequence_length: 8
    dataset:
      _target_: data.composed_dataset.ComposedDataset
      dataset_configs:
        - _target_: data.datasets.co3d.Co3dDataset
          split: test
          CO3D_DIR: /defaultShare/pubdata/3D/co3d
          CO3D_ANNOTATION_DIR: /defaultShare/pubdata/3D/co3d_anno

logging:
  log_dir: logs
  log_visuals: True
  log_freq: 10
  log_level_primary: DEBUG
  log_level_secondary: WARNING
  all_ranks: False
  tensorboard_writer:
    _target_: train_utils.tb_writer.TensorBoardLogger
    path: ${logging.log_dir}/tensorboard
  scalar_keys_to_log:
    train:
      keys_to_log:
        - loss_objective
        - loss_points
        - loss_confidence
        - loss_camera
        - loss_permutation_consistency
        - loss_scale_invariance
    val:
      keys_to_log:
        - loss_objective
        - loss_points
        - loss_confidence
        - loss_camera
        - loss_permutation_consistency
        - loss_scale_invariance
  log_visual_frequency:
    train: 100
    val: 50
  visuals_keys_to_log:
    train:
      keys_to_log:
        - images
        - local_points_vis
        - conf_vis
      modality: image
    val:
      keys_to_log:
        - images
        - local_points_vis
        - conf_vis
      modality: image
  visuals_per_batch_to_log: 4

checkpoint:
  save_dir: logs/${exp_name}/ckpts
  save_freq: 5
  resume_checkpoint_path: null  # Set to Pi3 pretrained checkpoint path if available
  strict: False

# Pi3-specific loss configuration
loss:
  _target_: loss.Pi3MultitaskLoss
  points:
    weight: 1.0
    loss_type: "smooth_l1"
    scale_invariant: True
    gradient_loss_fn: "normal"
    valid_range: 0.95
  confidence:
    weight: 0.5
    loss_type: "bce"
    confidence_threshold: 0.8
  camera:
    weight: 2.0
    loss_type: "geodesic"  # Geodesic loss for rotation matrices
    translation_weight: 1.0
    rotation_weight: 2.0
  permutation_consistency:
    weight: 0.1
    consistency_type: "pairwise"
  scale_invariance:
    weight: 0.05
    invariance_type: "relative"

# Pi3-specific optimization settings
optim:
  param_group_modifiers: True
  
  optimizer:
    _target_: torch.optim.AdamW
    lr: 3e-5  # Lower learning rate for Pi3's complex architecture
    weight_decay: 0.01
    betas: [0.9, 0.95]

  frozen_module_names:
    # Optionally freeze encoder layers during initial training
    # - "encoder.blocks.0.*"
    # - "encoder.blocks.1.*"

  amp:
    enabled: True
    amp_dtype: bfloat16
    
  gradient_clip:
    _target_: train_utils.gradient_clip.GradientClipper
    configs:
      - module_name: ["decoder"]
        max_norm: 1.0
        norm_type: 2
      - module_name: ["point_decoder", "conf_decoder", "camera_decoder"]
        max_norm: 0.5
        norm_type: 2
      - module_name: ["point_head", "conf_head", "camera_head"]
        max_norm: 0.5
        norm_type: 2
        
  options:
    lr:
      - scheduler:
          _target_: fvcore.common.param_scheduler.CompositeParamScheduler
          schedulers:
            - _target_: fvcore.common.param_scheduler.LinearParamScheduler
              start_value: 1e-8
              end_value: 3e-5
            - _target_: fvcore.common.param_scheduler.CosineParamScheduler
              start_value: 3e-5
              end_value: 1e-7
          lengths: [0.1, 0.9]  # Longer warmup for Pi3
          interval_scaling: ['rescaled', 'rescaled']
    weight_decay:
      - scheduler:
          _target_: fvcore.common.param_scheduler.ConstantParamScheduler
          value: 0.01

max_epochs: 50  # More epochs needed for Pi3's complex training

# Pi3 model configuration
model:
  _target_: pi3.models.pi3.Pi3
  pos_type: rope100
  decoder_size: large

distributed:
  backend: nccl
  comms_dtype: None
  find_unused_parameters: True  # Pi3 may have unused parameters during training
  timeout_mins: 60  # Longer timeout for Pi3's complex operations
  gradient_as_bucket_view: True
  bucket_cap_mb: 25
  broadcast_buffers: True

cuda:
  cudnn_deterministic: False
  cudnn_benchmark: True  # Enable for Pi3's consistent input sizes
  allow_tf32: True

# Pi3-specific training parameters
pi3_specific:
  rope_frequency: 100
  register_tokens: 5
  permutation_training: True
  scale_invariant_training: True
  confidence_training: True
  camera_pose_format: "matrix"  # 4x4 transformation matrices
  local_points_format: "xyz"    # 3D local coordinates
