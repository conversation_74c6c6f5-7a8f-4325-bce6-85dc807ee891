# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

import torch
import torch.nn.functional as F
import torchvision.transforms as transforms
import numpy as np
import random
from typing import Dict, List, Tuple, Optional, Union
import cv2


class Pi3AugmentationPipeline:
    """
    Comprehensive augmentation pipeline designed specifically for Pi3 training.
    
    This pipeline implements augmentations that preserve the geometric relationships
    required for Pi3's permutation-equivariant training while providing sufficient
    data diversity for robust learning.
    
    Key augmentation types:
    - Permutation-aware geometric augmentations
    - Scale-invariant transformations
    - Photometric augmentations
    - Confidence-preserving augmentations
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the Pi3 augmentation pipeline.
        
        Args:
            config: Configuration dictionary containing augmentation parameters
        """
        self.config = config
        
        # Geometric augmentation settings
        self.random_crop = config.get('random_crop', True)
        self.crop_probability = config.get('crop_probability', 0.7)
        self.crop_scale_range = config.get('crop_scale_range', [0.8, 1.0])
        
        # Photometric augmentation settings
        self.color_jitter = config.get('color_jitter', True)
        self.color_jitter_params = config.get('color_jitter_params', {
            'brightness': 0.2,
            'contrast': 0.2,
            'saturation': 0.1,
            'hue': 0.05
        })
        
        # Scale augmentation settings
        self.random_scale = config.get('random_scale', True)
        self.scale_range = config.get('scale_range', [0.5, 2.0])
        self.scale_probability = config.get('scale_probability', 0.6)
        
        # Permutation augmentation settings
        self.random_view_permutation = config.get('random_view_permutation', True)
        self.permutation_probability = config.get('permutation_probability', 0.8)
        
        # Initialize photometric transforms
        if self.color_jitter:
            self.color_transform = transforms.ColorJitter(
                brightness=self.color_jitter_params['brightness'],
                contrast=self.color_jitter_params['contrast'],
                saturation=self.color_jitter_params['saturation'],
                hue=self.color_jitter_params['hue']
            )

    def __call__(self, sample: Dict) -> Dict:
        """
        Apply the full augmentation pipeline to a sample.
        
        Args:
            sample: Input sample dictionary
            
        Returns:
            Augmented sample dictionary
        """
        # Apply geometric augmentations
        if self.random_crop and random.random() < self.crop_probability:
            sample = self._apply_random_crop(sample)
        
        # Apply photometric augmentations
        if self.color_jitter:
            sample = self._apply_color_jitter(sample)
        
        # Apply scale augmentations
        if self.random_scale and random.random() < self.scale_probability:
            sample = self._apply_scale_augmentation(sample)
        
        # Apply permutation augmentations
        if self.random_view_permutation and random.random() < self.permutation_probability:
            sample = self._apply_view_permutation(sample)
        
        return sample

    def _apply_random_crop(self, sample: Dict) -> Dict:
        """
        Apply random cropping while maintaining camera parameters consistency.
        
        This augmentation crops the images and adjusts the camera intrinsics
        accordingly to maintain geometric consistency.
        """
        if "images" not in sample:
            return sample
        
        images = sample["images"]  # (N, C, H, W)
        N, C, H, W = images.shape
        
        # Determine crop parameters
        scale = random.uniform(*self.crop_scale_range)
        crop_h = int(H * scale)
        crop_w = int(W * scale)
        
        # Random crop position
        top = random.randint(0, H - crop_h)
        left = random.randint(0, W - crop_w)
        
        # Apply crop to images
        cropped_images = images[:, :, top:top+crop_h, left:left+crop_w]
        
        # Resize back to original size
        cropped_images = F.interpolate(
            cropped_images, size=(H, W), mode='bilinear', align_corners=False
        )
        
        sample["images"] = cropped_images
        
        # Adjust camera intrinsics if available
        if "intrinsics" in sample:
            intrinsics = sample["intrinsics"].clone()  # (N, 3, 3)
            
            # Adjust principal point for crop
            intrinsics[:, 0, 2] = (intrinsics[:, 0, 2] - left) / scale  # cx
            intrinsics[:, 1, 2] = (intrinsics[:, 1, 2] - top) / scale   # cy
            
            # Adjust focal lengths for resize
            intrinsics[:, 0, 0] = intrinsics[:, 0, 0] / scale  # fx
            intrinsics[:, 1, 1] = intrinsics[:, 1, 1] / scale  # fy
            
            sample["intrinsics"] = intrinsics
        
        # Adjust depth maps if available
        if "depths" in sample:
            depths = sample["depths"]  # (N, H, W)
            cropped_depths = depths[:, top:top+crop_h, left:left+crop_w]
            cropped_depths = F.interpolate(
                cropped_depths.unsqueeze(1), size=(H, W), mode='bilinear', align_corners=False
            ).squeeze(1)
            sample["depths"] = cropped_depths
        
        # Adjust point masks if available
        if "point_masks" in sample:
            masks = sample["point_masks"]  # (N, H, W)
            cropped_masks = masks[:, top:top+crop_h, left:left+crop_w]
            cropped_masks = F.interpolate(
                cropped_masks.unsqueeze(1).float(), size=(H, W), mode='bilinear', align_corners=False
            ).squeeze(1)
            sample["point_masks"] = (cropped_masks > 0.5).bool()
        
        return sample

    def _apply_color_jitter(self, sample: Dict) -> Dict:
        """
        Apply photometric augmentations to images.
        
        This augmentation changes the appearance of images while preserving
        geometric relationships.
        """
        if "images" not in sample:
            return sample
        
        images = sample["images"]  # (N, C, H, W)
        N, C, H, W = images.shape
        
        # Apply color jitter to each image in the sequence
        augmented_images = []
        for i in range(N):
            img = images[i]  # (C, H, W)
            
            # Convert to PIL format for torchvision transforms
            img_pil = transforms.ToPILImage()(img)
            
            # Apply color jitter
            img_augmented = self.color_transform(img_pil)
            
            # Convert back to tensor
            img_tensor = transforms.ToTensor()(img_augmented)
            augmented_images.append(img_tensor)
        
        sample["images"] = torch.stack(augmented_images, dim=0)
        
        return sample

    def _apply_scale_augmentation(self, sample: Dict) -> Dict:
        """
        Apply scale augmentation to support scale-invariant training.
        
        This augmentation scales the 3D geometry while maintaining relative
        relationships between views.
        """
        # Generate random scale factor
        scale_factor = random.uniform(*self.scale_range)
        
        # Apply scaling to 3D world points if available
        if "world_points" in sample:
            sample["world_points"] = sample["world_points"] * scale_factor
        
        # Apply scaling to camera translations
        if "extrinsics" in sample:
            extrinsics = sample["extrinsics"].clone()  # (N, 4, 4)
            extrinsics[:, :3, 3] = extrinsics[:, :3, 3] * scale_factor
            sample["extrinsics"] = extrinsics
        
        # Apply scaling to depth maps if available
        if "depths" in sample:
            sample["depths"] = sample["depths"] * scale_factor
        
        # Store scale factor for potential use in loss computation
        sample["augmentation_scale_factor"] = scale_factor
        
        return sample

    def _apply_view_permutation(self, sample: Dict) -> Dict:
        """
        Apply random permutation to the sequence of views.
        
        This is a key augmentation for Pi3's permutation-equivariant training,
        ensuring the model learns representations invariant to view ordering.
        """
        if "images" not in sample:
            return sample
        
        N = sample["images"].shape[0]  # Number of views
        
        # Generate random permutation
        perm_indices = torch.randperm(N)
        
        # Apply permutation to all relevant tensors
        tensor_keys = ["images", "extrinsics", "intrinsics", "depths", "world_points", "point_masks"]
        
        for key in tensor_keys:
            if key in sample and isinstance(sample[key], torch.Tensor):
                sample[key] = sample[key][perm_indices]
        
        # Store permutation information
        sample["permutation_indices"] = perm_indices
        sample["has_view_permutation"] = True
        
        return sample

    def _apply_noise_augmentation(self, sample: Dict) -> Dict:
        """
        Apply noise augmentation to improve robustness.
        
        This adds controlled noise to various modalities while preserving
        the overall geometric structure.
        """
        # Add Gaussian noise to images
        if "images" in sample and random.random() < 0.3:
            noise_std = random.uniform(0.01, 0.05)
            noise = torch.randn_like(sample["images"]) * noise_std
            sample["images"] = torch.clamp(sample["images"] + noise, 0, 1)
        
        # Add small perturbations to camera parameters
        if "extrinsics" in sample and random.random() < 0.2:
            # Small rotation perturbations
            rotation_noise_std = 0.01  # ~0.6 degrees
            translation_noise_std = 0.05
            
            extrinsics = sample["extrinsics"].clone()
            N = extrinsics.shape[0]
            
            for i in range(N):
                # Add rotation noise (axis-angle representation)
                axis_angle = torch.randn(3) * rotation_noise_std
                rotation_matrix = self._axis_angle_to_rotation_matrix(axis_angle)
                
                # Apply rotation perturbation
                extrinsics[i, :3, :3] = torch.mm(rotation_matrix, extrinsics[i, :3, :3])
                
                # Add translation noise
                translation_noise = torch.randn(3) * translation_noise_std
                extrinsics[i, :3, 3] += translation_noise
            
            sample["extrinsics"] = extrinsics
        
        return sample

    def _axis_angle_to_rotation_matrix(self, axis_angle: torch.Tensor) -> torch.Tensor:
        """
        Convert axis-angle representation to rotation matrix.
        
        Args:
            axis_angle: (3,) tensor representing rotation in axis-angle form
            
        Returns:
            (3, 3) rotation matrix
        """
        angle = torch.norm(axis_angle)
        if angle < 1e-8:
            return torch.eye(3, dtype=axis_angle.dtype, device=axis_angle.device)
        
        axis = axis_angle / angle
        cos_angle = torch.cos(angle)
        sin_angle = torch.sin(angle)
        
        # Rodrigues' rotation formula
        K = torch.tensor([
            [0, -axis[2], axis[1]],
            [axis[2], 0, -axis[0]],
            [-axis[1], axis[0], 0]
        ], dtype=axis_angle.dtype, device=axis_angle.device)
        
        R = torch.eye(3, dtype=axis_angle.dtype, device=axis_angle.device) + \
            sin_angle * K + (1 - cos_angle) * torch.mm(K, K)
        
        return R

    def _apply_temporal_augmentation(self, sample: Dict) -> Dict:
        """
        Apply temporal augmentations for video sequences.
        
        This includes frame dropping, temporal reordering, and temporal
        subsampling while maintaining the permutation-equivariant property.
        """
        if "images" not in sample:
            return sample
        
        N = sample["images"].shape[0]
        
        # Random temporal subsampling
        if N > 4 and random.random() < 0.3:
            # Keep at least 3 frames
            num_keep = random.randint(3, N)
            indices = torch.randperm(N)[:num_keep]
            indices = torch.sort(indices)[0]  # Maintain temporal order
            
            # Apply subsampling to all relevant tensors
            tensor_keys = ["images", "extrinsics", "intrinsics", "depths", "world_points", "point_masks"]
            
            for key in tensor_keys:
                if key in sample and isinstance(sample[key], torch.Tensor):
                    sample[key] = sample[key][indices]
        
        return sample
