# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

import torch
import torch.nn.functional as F
import numpy as np
import random
from typing import Dict, List, Tuple, Optional, Any
from torch.utils.data import DataLoader, Dataset
import logging

# Import VGGT data utilities
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../vggt/training/data'))

from dynamic_dataloader import DynamicTorchDataset
from dataset_util import *


class Pi3DynamicTorchDataset(DynamicTorchDataset):
    """
    Pi3-specific dynamic dataset that extends VGGT's data loading capabilities.
    
    This dataset implements permutation-equivariant data loading and augmentation
    strategies specifically designed for Pi3's training requirements.
    
    Key features:
    - Permutation-aware batch construction
    - Scale-invariant data augmentation
    - Confidence-based sample weighting
    - Variable sequence length support
    """
    
    def __init__(self, common_config: Dict, dataset: Dataset, **kwargs):
        """
        Initialize Pi3 dataset with specialized configurations.
        
        Args:
            common_config: Common configuration dictionary
            dataset: Underlying dataset instance
            **kwargs: Additional arguments
        """
        super().__init__(common_config, dataset, **kwargs)
        
        # Pi3-specific configurations
        self.permutation_augment = common_config.get('permutation_augment', True)
        self.max_sequence_length = common_config.get('max_sequence_length', 8)
        self.scale_augment = common_config.get('scale_augment', True)
        self.confidence_weighting = common_config.get('confidence_weighting', True)
        
        logging.info(f"Pi3 Dataset initialized with:")
        logging.info(f"  - Permutation augmentation: {self.permutation_augment}")
        logging.info(f"  - Max sequence length: {self.max_sequence_length}")
        logging.info(f"  - Scale augmentation: {self.scale_augment}")
        logging.info(f"  - Confidence weighting: {self.confidence_weighting}")

    def get_loader(self, epoch: int = 0) -> DataLoader:
        """
        Get data loader with Pi3-specific configurations.
        
        Args:
            epoch: Current training epoch
            
        Returns:
            Configured DataLoader instance
        """
        # Set epoch-dependent random seed for reproducible permutations
        if hasattr(self, 'seed'):
            torch.manual_seed(self.seed + epoch * 1000)
            np.random.seed(self.seed + epoch * 1000)
            random.seed(self.seed + epoch * 1000)
        
        # Create base loader
        loader = super().get_loader(epoch)
        
        # Wrap with Pi3-specific collate function
        loader.collate_fn = self._pi3_collate_fn
        
        return loader

    def _pi3_collate_fn(self, batch_list: List[Dict]) -> Dict:
        """
        Pi3-specific collate function that handles permutation-equivariant batching.
        
        This function ensures that batches are constructed in a way that supports
        Pi3's permutation-equivariant training while maintaining efficiency.
        
        Args:
            batch_list: List of individual samples
            
        Returns:
            Collated batch dictionary
        """
        # First apply standard collation
        batch = self._standard_collate(batch_list)
        
        # Apply Pi3-specific processing
        batch = self._apply_pi3_batch_processing(batch)
        
        return batch

    def _standard_collate(self, batch_list: List[Dict]) -> Dict:
        """
        Standard collation function adapted from VGGT.
        
        This handles the basic tensor stacking and padding operations.
        """
        if len(batch_list) == 0:
            return {}
        
        # Get keys from first sample
        keys = batch_list[0].keys()
        collated_batch = {}
        
        for key in keys:
            values = [sample[key] for sample in batch_list]
            
            if isinstance(values[0], torch.Tensor):
                # Handle tensor collation with potential padding
                collated_batch[key] = self._collate_tensors(values, key)
            elif isinstance(values[0], (list, tuple)):
                # Handle sequence data
                collated_batch[key] = values
            else:
                # Handle other data types
                collated_batch[key] = values
        
        return collated_batch

    def _collate_tensors(self, tensor_list: List[torch.Tensor], key: str) -> torch.Tensor:
        """
        Collate tensors with appropriate padding and stacking.
        
        Args:
            tensor_list: List of tensors to collate
            key: Key name for context-specific handling
            
        Returns:
            Collated tensor
        """
        if len(tensor_list) == 0:
            return torch.empty(0)
        
        # Check if all tensors have the same shape
        shapes = [t.shape for t in tensor_list]
        if all(shape == shapes[0] for shape in shapes):
            # Simple stacking if shapes match
            return torch.stack(tensor_list, dim=0)
        
        # Handle variable sequence lengths
        if key in ['images', 'extrinsics', 'intrinsics', 'depths', 'world_points', 'point_masks']:
            return self._pad_and_stack_sequences(tensor_list, key)
        
        # Default stacking
        try:
            return torch.stack(tensor_list, dim=0)
        except RuntimeError:
            # If stacking fails, return as list
            return tensor_list

    def _pad_and_stack_sequences(self, tensor_list: List[torch.Tensor], key: str) -> torch.Tensor:
        """
        Pad and stack sequences to handle variable lengths.
        
        This is important for Pi3 as it needs to handle sequences of different lengths
        while maintaining the permutation-equivariant property.
        """
        if len(tensor_list) == 0:
            return torch.empty(0)
        
        # Find maximum sequence length
        max_seq_len = max(t.shape[0] for t in tensor_list)
        max_seq_len = min(max_seq_len, self.max_sequence_length)
        
        padded_tensors = []
        
        for tensor in tensor_list:
            seq_len = tensor.shape[0]
            
            if seq_len > max_seq_len:
                # Randomly sample frames if sequence is too long
                indices = torch.randperm(seq_len)[:max_seq_len]
                indices = torch.sort(indices)[0]  # Keep temporal order
                tensor = tensor[indices]
            elif seq_len < max_seq_len:
                # Pad sequence if too short
                pad_size = max_seq_len - seq_len
                if key == 'images':
                    # Pad with zeros for images
                    pad_shape = (pad_size,) + tensor.shape[1:]
                    padding = torch.zeros(pad_shape, dtype=tensor.dtype, device=tensor.device)
                elif key in ['extrinsics', 'intrinsics']:
                    # Pad with identity matrices for camera parameters
                    pad_shape = (pad_size,) + tensor.shape[1:]
                    padding = torch.eye(tensor.shape[-1], dtype=tensor.dtype, device=tensor.device)
                    padding = padding.unsqueeze(0).expand(pad_shape)
                else:
                    # Default zero padding
                    pad_shape = (pad_size,) + tensor.shape[1:]
                    padding = torch.zeros(pad_shape, dtype=tensor.dtype, device=tensor.device)
                
                tensor = torch.cat([tensor, padding], dim=0)
            
            padded_tensors.append(tensor)
        
        return torch.stack(padded_tensors, dim=0)

    def _apply_pi3_batch_processing(self, batch: Dict) -> Dict:
        """
        Apply Pi3-specific batch processing.
        
        This includes permutation augmentation, scale normalization,
        and confidence weighting.
        """
        # Apply permutation augmentation
        if self.permutation_augment and self.training:
            batch = self._apply_batch_permutation_augmentation(batch)
        
        # Apply scale augmentation
        if self.scale_augment and self.training:
            batch = self._apply_batch_scale_augmentation(batch)
        
        # Generate confidence weights
        if self.confidence_weighting:
            batch = self._generate_batch_confidence_weights(batch)
        
        # Add Pi3-specific metadata
        batch = self._add_pi3_metadata(batch)
        
        return batch

    def _apply_batch_permutation_augmentation(self, batch: Dict) -> Dict:
        """
        Apply permutation augmentation at the batch level.
        
        This ensures that the model learns to be invariant to the ordering
        of input views, which is crucial for Pi3's permutation-equivariant design.
        """
        if "images" not in batch:
            return batch
        
        B, N = batch["images"].shape[:2]
        
        # Generate permutations for each batch element
        permutation_masks = []
        for b in range(B):
            if torch.rand(1).item() < 0.7:  # 70% chance to apply permutation
                perm_mask = torch.randperm(N)
            else:
                perm_mask = torch.arange(N)
            permutation_masks.append(perm_mask)
        
        # Apply permutations to relevant tensors
        tensor_keys = ["images", "extrinsics", "intrinsics", "depths"]
        if "world_points" in batch:
            tensor_keys.append("world_points")
        if "point_masks" in batch:
            tensor_keys.append("point_masks")
        
        for key in tensor_keys:
            if key in batch and isinstance(batch[key], torch.Tensor):
                original_tensor = batch[key]
                permuted_tensors = []
                
                for b in range(B):
                    perm_mask = permutation_masks[b]
                    permuted_tensor = original_tensor[b][perm_mask]
                    permuted_tensors.append(permuted_tensor)
                
                batch[key] = torch.stack(permuted_tensors, dim=0)
        
        # Store permutation information
        batch["permutation_masks"] = torch.stack(permutation_masks, dim=0)
        batch["has_permutation_augmentation"] = True
        
        return batch

    def _apply_batch_scale_augmentation(self, batch: Dict) -> Dict:
        """
        Apply scale augmentation to support scale-invariant training.
        
        This helps Pi3 learn representations that are robust to scale changes.
        """
        if "world_points" not in batch or "extrinsics" not in batch:
            return batch
        
        B = batch["images"].shape[0]
        
        # Generate random scale factors for each batch element
        scale_factors = torch.rand(B, device=batch["images"].device) * 1.5 + 0.5  # [0.5, 2.0]
        
        # Apply scaling to world points and camera translations
        if "world_points" in batch:
            batch["world_points"] = batch["world_points"] * scale_factors.view(B, 1, 1, 1, 1)
        
        if "extrinsics" in batch:
            batch["extrinsics"] = batch["extrinsics"].clone()
            batch["extrinsics"][..., :3, 3] = batch["extrinsics"][..., :3, 3] * scale_factors.view(B, 1, 1)
        
        # Store scale factors for potential use in loss computation
        batch["scale_augmentation_factors"] = scale_factors
        
        return batch

    def _generate_batch_confidence_weights(self, batch: Dict) -> Dict:
        """
        Generate confidence weights for the batch.
        
        These weights help the model focus on high-quality samples and
        handle uncertainty in the training data.
        """
        if "images" not in batch:
            return batch
        
        B, N, C, H, W = batch["images"].shape
        
        # Initialize confidence weights
        confidence_weights = torch.ones(B, N, H, W, device=batch["images"].device)
        
        # Reduce confidence near image boundaries
        boundary_margin = 10
        confidence_weights[:, :, :boundary_margin, :] *= 0.7
        confidence_weights[:, :, -boundary_margin:, :] *= 0.7
        confidence_weights[:, :, :, :boundary_margin] *= 0.7
        confidence_weights[:, :, :, -boundary_margin:] *= 0.7
        
        # Use existing point masks if available
        if "point_masks" in batch:
            point_confidence = batch["point_masks"].float()
            confidence_weights *= (0.5 + 0.5 * point_confidence)
        
        batch["confidence_weights"] = confidence_weights
        
        return batch

    def _add_pi3_metadata(self, batch: Dict) -> Dict:
        """
        Add Pi3-specific metadata to the batch.
        
        This metadata helps with loss computation and debugging.
        """
        # Add sequence length information
        if "images" in batch:
            batch["sequence_lengths"] = torch.tensor([batch["images"].shape[1]] * batch["images"].shape[0])
        
        # Add training phase indicator
        batch["is_training"] = self.training
        
        # Add dataset type
        batch["dataset_type"] = "pi3"
        
        return batch
