#!/usr/bin/env python3
# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

"""
Pi3 Training Usage Examples

This script demonstrates various ways to use the Pi3 training implementation,
including basic training, fine-tuning, and evaluation scenarios.

Usage:
    python example_usage.py --example basic_training
    python example_usage.py --example fine_tuning
    python example_usage.py --example evaluation
"""

import os
import sys
import argparse
import torch
import logging
from pathlib import Path

# Add project paths
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# Import Pi3 components
from loss import Pi3MultitaskLoss
from pi3_trainer import Pi3<PERSON>rainer
from data.pi3_dataloader import Pi3DynamicTorchDataset


def setup_logging():
    """Setup logging for examples."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def example_basic_training():
    """
    Example 1: Basic Pi3 training from scratch.
    
    This example shows how to set up and run basic Pi3 training
    with default parameters.
    """
    logging.info("=" * 60)
    logging.info("Example 1: Basic Pi3 Training")
    logging.info("=" * 60)
    
    # Create minimal configuration
    config = {
        "exp_name": "pi3_basic_example",
        "img_size": 256,  # Smaller for example
        "max_epochs": 5,  # Few epochs for example
        "seed_value": 42,
        
        # Model configuration
        "model": {
            "_target_": "pi3.models.pi3.Pi3",
            "pos_type": "rope100",
            "decoder_size": "base"  # Smaller model for example
        },
        
        # Loss configuration
        "loss": {
            "_target_": "loss.Pi3MultitaskLoss",
            "points": {"weight": 1.0, "scale_invariant": True},
            "confidence": {"weight": 0.5},
            "camera": {"weight": 2.0, "loss_type": "geodesic"}
        },
        
        # Optimizer configuration
        "optim": {
            "optimizer": {
                "_target_": "torch.optim.AdamW",
                "lr": 1e-4,
                "weight_decay": 0.01
            },
            "amp": {"enabled": True, "amp_dtype": "bfloat16"}
        },
        
        # Data configuration (dummy for example)
        "data": {
            "train": {
                "_target_": "data.pi3_dataloader.Pi3DynamicTorchDataset",
                "common_config": {
                    "img_size": 256,
                    "patch_size": 14,
                    "permutation_augment": True,
                    "max_sequence_length": 4
                }
            }
        },
        
        # Logging configuration
        "logging": {
            "log_dir": "logs/pi3_basic_example",
            "log_visuals": True,
            "log_freq": 10
        },
        
        # Checkpoint configuration
        "checkpoint": {
            "save_dir": "logs/pi3_basic_example/ckpts",
            "save_freq": 2
        }
    }
    
    logging.info("Configuration created successfully")
    logging.info("In a real scenario, you would:")
    logging.info("1. Prepare your dataset (CO3D, custom data, etc.)")
    logging.info("2. Create proper data loaders")
    logging.info("3. Run: python train_pi3.py --config your_config.yaml")
    
    return config


def example_fine_tuning():
    """
    Example 2: Fine-tuning a pre-trained Pi3 model.
    
    This example shows how to fine-tune a pre-trained Pi3 model
    on a specific dataset or task.
    """
    logging.info("=" * 60)
    logging.info("Example 2: Pi3 Fine-tuning")
    logging.info("=" * 60)
    
    # Fine-tuning configuration
    config = {
        "exp_name": "pi3_finetune_example",
        "img_size": 518,
        "max_epochs": 20,
        "seed_value": 42,
        
        # Model configuration
        "model": {
            "_target_": "pi3.models.pi3.Pi3",
            "pos_type": "rope100",
            "decoder_size": "large"
        },
        
        # Loss configuration with adjusted weights for fine-tuning
        "loss": {
            "_target_": "loss.Pi3MultitaskLoss",
            "points": {"weight": 1.0, "scale_invariant": True, "gradient_loss_fn": "normal"},
            "confidence": {"weight": 0.3},  # Reduced for fine-tuning
            "camera": {"weight": 1.5, "loss_type": "geodesic"},
            "permutation_consistency": {"weight": 0.05},  # Lower for stability
            "scale_invariance": {"weight": 0.02}
        },
        
        # Optimizer configuration for fine-tuning
        "optim": {
            "optimizer": {
                "_target_": "torch.optim.AdamW",
                "lr": 1e-5,  # Lower learning rate for fine-tuning
                "weight_decay": 0.005
            },
            "frozen_module_names": [
                # Optionally freeze some layers
                # "encoder.blocks.0.*",
                # "encoder.blocks.1.*"
            ],
            "amp": {"enabled": True, "amp_dtype": "bfloat16"}
        },
        
        # Checkpoint configuration for fine-tuning
        "checkpoint": {
            "save_dir": "logs/pi3_finetune_example/ckpts",
            "save_freq": 5,
            "resume_checkpoint_path": "path/to/pretrained/pi3_model.pt",  # Pre-trained model
            "strict": False  # Allow partial loading
        }
    }
    
    logging.info("Fine-tuning configuration created")
    logging.info("Key fine-tuning considerations:")
    logging.info("1. Lower learning rate (1e-5 vs 3e-5)")
    logging.info("2. Adjusted loss weights for stability")
    logging.info("3. Optional layer freezing")
    logging.info("4. Pre-trained checkpoint loading")
    
    return config


def example_evaluation():
    """
    Example 3: Evaluating a trained Pi3 model.
    
    This example shows how to evaluate a trained Pi3 model
    on validation/test data.
    """
    logging.info("=" * 60)
    logging.info("Example 3: Pi3 Model Evaluation")
    logging.info("=" * 60)
    
    # Evaluation configuration
    config = {
        "exp_name": "pi3_eval_example",
        "mode": "val",  # Validation only
        "img_size": 518,
        "seed_value": 42,
        
        # Model configuration
        "model": {
            "_target_": "pi3.models.pi3.Pi3",
            "pos_type": "rope100",
            "decoder_size": "large"
        },
        
        # Data configuration for evaluation
        "data": {
            "val": {
                "_target_": "data.pi3_dataloader.Pi3DynamicTorchDataset",
                "common_config": {
                    "img_size": 518,
                    "patch_size": 14,
                    "permutation_augment": False,  # No augmentation for evaluation
                    "max_sequence_length": 8
                }
            }
        },
        
        # Logging configuration
        "logging": {
            "log_dir": "logs/pi3_eval_example",
            "log_visuals": True,
            "log_freq": 1
        },
        
        # Checkpoint configuration
        "checkpoint": {
            "resume_checkpoint_path": "path/to/trained/pi3_model.pt",
            "strict": True
        }
    }
    
    logging.info("Evaluation configuration created")
    logging.info("Key evaluation settings:")
    logging.info("1. mode='val' for validation only")
    logging.info("2. No data augmentation")
    logging.info("3. Load trained checkpoint")
    logging.info("4. Enhanced logging for analysis")
    
    return config


def example_loss_function_usage():
    """
    Example 4: Using Pi3 loss functions directly.
    
    This example demonstrates how to use Pi3 loss functions
    in custom training loops or for analysis.
    """
    logging.info("=" * 60)
    logging.info("Example 4: Pi3 Loss Function Usage")
    logging.info("=" * 60)
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create loss function
    loss_fn = Pi3MultitaskLoss(
        points={"weight": 1.0, "loss_type": "smooth_l1", "scale_invariant": True},
        confidence={"weight": 0.5, "loss_type": "bce"},
        camera={"weight": 2.0, "loss_type": "geodesic"},
        permutation_consistency={"weight": 0.1, "consistency_type": "pairwise"},
        scale_invariance={"weight": 0.05, "invariance_type": "relative"}
    ).to(device)
    
    # Create dummy data
    batch_size, num_views, height, width = 2, 4, 64, 64
    
    predictions = {
        "points": torch.randn(batch_size, num_views, height, width, 3, device=device),
        "local_points": torch.randn(batch_size, num_views, height, width, 3, device=device),
        "conf": torch.sigmoid(torch.randn(batch_size, num_views, height, width, 1, device=device)),
        "camera_poses": torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).expand(batch_size, num_views, -1, -1)
    }
    
    batch = {
        "world_points": torch.randn(batch_size, num_views, height, width, 3, device=device),
        "point_masks": torch.rand(batch_size, num_views, height, width, device=device) > 0.3,
        "camera_poses": torch.eye(4, device=device).unsqueeze(0).unsqueeze(0).expand(batch_size, num_views, -1, -1),
        "permuted_views_available": True
    }
    
    # Compute loss
    loss_dict = loss_fn(predictions, batch)
    
    logging.info("Loss computation successful!")
    logging.info("Loss components:")
    for key, value in loss_dict.items():
        if isinstance(value, torch.Tensor):
            logging.info(f"  {key}: {value.item():.6f}")
    
    return loss_dict


def example_data_augmentation():
    """
    Example 5: Pi3 data augmentation pipeline.
    
    This example shows how to use Pi3's augmentation pipeline
    for data preprocessing.
    """
    logging.info("=" * 60)
    logging.info("Example 5: Pi3 Data Augmentation")
    logging.info("=" * 60)
    
    from data.pi3_augmentation import Pi3AugmentationPipeline
    
    # Create augmentation pipeline
    config = {
        "random_crop": True,
        "crop_probability": 0.7,
        "crop_scale_range": [0.8, 1.0],
        "color_jitter": True,
        "color_jitter_params": {
            "brightness": 0.2,
            "contrast": 0.2,
            "saturation": 0.1,
            "hue": 0.05
        },
        "random_scale": True,
        "scale_range": [0.5, 2.0],
        "scale_probability": 0.6,
        "random_view_permutation": True,
        "permutation_probability": 0.8
    }
    
    augmentation = Pi3AugmentationPipeline(config)
    
    # Create dummy sample
    sample = {
        "images": torch.rand(4, 3, 256, 256),
        "extrinsics": torch.eye(4).unsqueeze(0).expand(4, -1, -1),
        "intrinsics": torch.eye(3).unsqueeze(0).expand(4, -1, -1),
        "depths": torch.rand(4, 256, 256),
        "world_points": torch.randn(4, 256, 256, 3),
        "point_masks": torch.rand(4, 256, 256) > 0.3
    }
    
    # Apply augmentation
    augmented_sample = augmentation(sample)
    
    logging.info("Augmentation applied successfully!")
    logging.info("Sample keys:", list(sample.keys()))
    logging.info("Augmented sample keys:", list(augmented_sample.keys()))
    
    if "has_view_permutation" in augmented_sample:
        logging.info("View permutation applied")
    if "augmentation_scale_factor" in augmented_sample:
        logging.info(f"Scale factor: {augmented_sample['augmentation_scale_factor']}")
    
    return augmented_sample


def main():
    """Main function to run examples."""
    setup_logging()
    
    parser = argparse.ArgumentParser(description="Pi3 Training Usage Examples")
    parser.add_argument(
        "--example",
        type=str,
        choices=["basic_training", "fine_tuning", "evaluation", "loss_usage", "augmentation", "all"],
        default="all",
        help="Which example to run"
    )
    
    args = parser.parse_args()
    
    examples = {
        "basic_training": example_basic_training,
        "fine_tuning": example_fine_tuning,
        "evaluation": example_evaluation,
        "loss_usage": example_loss_function_usage,
        "augmentation": example_data_augmentation
    }
    
    if args.example == "all":
        logging.info("Running all examples...")
        for name, func in examples.items():
            try:
                func()
                logging.info(f"✓ {name} completed successfully\n")
            except Exception as e:
                logging.error(f"✗ {name} failed: {e}\n")
    else:
        try:
            examples[args.example]()
            logging.info(f"✓ {args.example} completed successfully")
        except Exception as e:
            logging.error(f"✗ {args.example} failed: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
