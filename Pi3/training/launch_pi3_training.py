#!/usr/bin/env python3
# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

"""
Simplified Pi3 Training Launcher

This script provides a simplified interface for launching Pi3 training
with sensible defaults and automatic configuration.

Usage:
    # Single GPU training
    python launch_pi3_training.py
    
    # Multi-GPU training
    python launch_pi3_training.py --gpus 4
    
    # Custom configuration
    python launch_pi3_training.py --config custom_config.yaml --gpus 2
    
    # Resume training
    python launch_pi3_training.py --resume checkpoints/latest.pt
"""

import os
import sys
import argparse
import subprocess
import logging
from pathlib import Path
import torch


def setup_logging():
    """Setup basic logging for the launcher."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Pi3 Training Launcher",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config/pi3_default.yaml",
        help="Path to training configuration file"
    )
    
    parser.add_argument(
        "--gpus",
        type=int,
        default=1,
        help="Number of GPUs to use for training"
    )
    
    parser.add_argument(
        "--resume",
        type=str,
        default=None,
        help="Path to checkpoint to resume from"
    )
    
    parser.add_argument(
        "--exp_name",
        type=str,
        default=None,
        help="Experiment name (overrides config)"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )
    
    parser.add_argument(
        "--validate_only",
        action="store_true",
        help="Run validation only"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=29500,
        help="Master port for distributed training"
    )
    
    return parser.parse_args()


def check_environment():
    """Check if the environment is properly set up for Pi3 training."""
    # Check CUDA availability
    if not torch.cuda.is_available():
        logging.warning("CUDA is not available. Training will be very slow on CPU.")
        return False
    
    # Check GPU count
    gpu_count = torch.cuda.device_count()
    logging.info(f"Found {gpu_count} GPU(s)")
    
    # Check required directories
    current_dir = Path(__file__).parent
    required_dirs = [
        current_dir / "config",
        current_dir / "data",
        current_dir.parent / "pi3" / "models"
    ]
    
    for dir_path in required_dirs:
        if not dir_path.exists():
            logging.error(f"Required directory not found: {dir_path}")
            return False
    
    # Check required files
    required_files = [
        current_dir / "train_pi3.py",
        current_dir / "pi3_trainer.py",
        current_dir / "loss.py"
    ]
    
    for file_path in required_files:
        if not file_path.exists():
            logging.error(f"Required file not found: {file_path}")
            return False
    
    return True


def build_training_command(args):
    """Build the training command based on arguments."""
    current_dir = Path(__file__).parent
    train_script = current_dir / "train_pi3.py"
    
    # Base command
    if args.gpus > 1:
        # Multi-GPU distributed training
        cmd = [
            "python", "-m", "torch.distributed.launch",
            f"--nproc_per_node={args.gpus}",
            f"--master_port={args.port}",
            str(train_script),
            "--distributed"
        ]
    else:
        # Single GPU training
        cmd = ["python", str(train_script)]
    
    # Add configuration
    cmd.extend(["--config", args.config])
    
    # Add optional arguments
    if args.resume:
        cmd.extend(["--resume", args.resume])
    
    if args.debug:
        cmd.append("--debug")
    
    if args.validate_only:
        cmd.append("--validate_only")
    
    return cmd


def create_default_config_if_needed(config_path):
    """Create default configuration if it doesn't exist."""
    if os.path.exists(config_path):
        return
    
    logging.info(f"Creating default configuration at {config_path}")
    
    # Ensure config directory exists
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    # Create minimal default config
    default_config = """# Pi3 Training Configuration
defaults:
  - pi3_dataset.yaml

exp_name: pi3_default_exp
img_size: 518
num_workers: 4
seed_value: 42
accum_steps: 2
patch_size: 14
val_epoch_freq: 5
max_img_per_gpu: 16
max_epochs: 30

# Model configuration
model:
  _target_: pi3.models.pi3.Pi3
  pos_type: rope100
  decoder_size: large

# Loss configuration
loss:
  _target_: loss.Pi3MultitaskLoss
  points:
    weight: 1.0
    loss_type: "smooth_l1"
    scale_invariant: True
  confidence:
    weight: 0.5
    loss_type: "bce"
  camera:
    weight: 2.0
    loss_type: "geodesic"

# Optimizer configuration
optim:
  optimizer:
    _target_: torch.optim.AdamW
    lr: 3e-5
    weight_decay: 0.01
  amp:
    enabled: True
    amp_dtype: bfloat16

# Logging configuration
logging:
  log_dir: logs
  log_visuals: True
  log_freq: 10
  tensorboard_writer:
    _target_: train_utils.tb_writer.TensorBoardLogger
    path: ${logging.log_dir}/tensorboard

# Checkpoint configuration
checkpoint:
  save_dir: logs/${exp_name}/ckpts
  save_freq: 5
  strict: False

# Distributed training configuration
distributed:
  backend: nccl
  find_unused_parameters: True
  timeout_mins: 60

# CUDA configuration
cuda:
  cudnn_deterministic: False
  cudnn_benchmark: True
  allow_tf32: True
"""
    
    with open(config_path, 'w') as f:
        f.write(default_config)
    
    logging.info(f"Default configuration created at {config_path}")


def main():
    """Main launcher function."""
    setup_logging()
    
    logging.info("Pi3 Training Launcher")
    logging.info("=" * 50)
    
    # Parse arguments
    args = parse_arguments()
    
    # Check environment
    if not check_environment():
        logging.error("Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Create default config if needed
    create_default_config_if_needed(args.config)
    
    # Validate GPU count
    available_gpus = torch.cuda.device_count()
    if args.gpus > available_gpus:
        logging.warning(f"Requested {args.gpus} GPUs but only {available_gpus} available. Using {available_gpus} GPUs.")
        args.gpus = available_gpus
    
    # Build training command
    cmd = build_training_command(args)
    
    # Log training setup
    logging.info(f"Configuration: {args.config}")
    logging.info(f"GPUs: {args.gpus}")
    logging.info(f"Resume: {args.resume}")
    logging.info(f"Debug mode: {args.debug}")
    logging.info(f"Validation only: {args.validate_only}")
    logging.info(f"Command: {' '.join(cmd)}")
    
    # Launch training
    try:
        logging.info("Launching Pi3 training...")
        result = subprocess.run(cmd, check=True)
        logging.info("Training completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        logging.error(f"Training failed with return code {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        logging.info("Training interrupted by user")
        return 0
    except Exception as e:
        logging.error(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
