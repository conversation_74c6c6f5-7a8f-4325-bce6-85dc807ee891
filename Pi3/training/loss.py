# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from dataclasses import dataclass
from typing import Dict, Optional, Tuple, List
from math import pi


@dataclass(eq=False)
class Pi3MultitaskLoss(torch.nn.Module):
    """
    Multi-task loss module specifically designed for Pi3 training.
    
    Implements permutation-equivariant losses that are crucial for Pi3's
    reference-view-free training paradigm. The loss function ensures that
    the model learns consistent representations regardless of input ordering.
    
    Key components:
    - Point reconstruction loss with scale invariance
    - Confidence-weighted losses
    - Camera pose loss with geodesic distance
    - Permutation consistency loss
    - Scale invariance loss
    """
    
    def __init__(
        self, 
        points=None, 
        confidence=None, 
        camera=None, 
        permutation_consistency=None,
        scale_invariance=None,
        **kwargs
    ):
        super().__init__()
        self.points = points
        self.confidence = confidence
        self.camera = camera
        self.permutation_consistency = permutation_consistency
        self.scale_invariance = scale_invariance

    def forward(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute the total multi-task loss for Pi3 training.
        
        Args:
            predictions: Dict containing Pi3 model predictions:
                - points: Global 3D points (B, N, H, W, 3)
                - local_points: Local 3D coordinates (B, N, H, W, 3)  
                - conf: Confidence scores (B, N, H, W, 1)
                - camera_poses: Camera transformation matrices (B, N, 4, 4)
            batch: Dict containing ground truth data and metadata
            
        Returns:
            Dict containing individual losses and total objective
        """
        total_loss = 0
        loss_dict = {}
        
        # Point reconstruction loss - core geometric loss
        if "points" in predictions and self.points is not None:
            points_loss_dict = self._compute_points_loss(predictions, batch)
            points_loss = points_loss_dict["loss_points"] * self.points["weight"]
            total_loss += points_loss
            loss_dict.update(points_loss_dict)
        
        # Confidence loss - helps with uncertainty estimation
        if "conf" in predictions and self.confidence is not None:
            conf_loss_dict = self._compute_confidence_loss(predictions, batch)
            conf_loss = conf_loss_dict["loss_confidence"] * self.confidence["weight"]
            total_loss += conf_loss
            loss_dict.update(conf_loss_dict)
        
        # Camera pose loss - ensures accurate pose estimation
        if "camera_poses" in predictions and self.camera is not None:
            camera_loss_dict = self._compute_camera_loss(predictions, batch)
            camera_loss = camera_loss_dict["loss_camera"] * self.camera["weight"]
            total_loss += camera_loss
            loss_dict.update(camera_loss_dict)
        
        # Permutation consistency loss - key for Pi3's equivariance
        if self.permutation_consistency is not None:
            perm_loss_dict = self._compute_permutation_consistency_loss(predictions, batch)
            perm_loss = perm_loss_dict["loss_permutation_consistency"] * self.permutation_consistency["weight"]
            total_loss += perm_loss
            loss_dict.update(perm_loss_dict)
        
        # Scale invariance loss - ensures scale-invariant representations
        if self.scale_invariance is not None:
            scale_loss_dict = self._compute_scale_invariance_loss(predictions, batch)
            scale_loss = scale_loss_dict["loss_scale_invariance"] * self.scale_invariance["weight"]
            total_loss += scale_loss
            loss_dict.update(scale_loss_dict)
        
        loss_dict["objective"] = total_loss
        return loss_dict

    def _compute_points_loss(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute point reconstruction loss with scale invariance and confidence weighting.
        
        This loss ensures that the predicted 3D points accurately represent the scene
        geometry while being invariant to global scale changes.
        """
        pred_points = predictions["points"]  # (B, N, H, W, 3)
        pred_local_points = predictions["local_points"]  # (B, N, H, W, 3)
        
        # Get ground truth points from batch (assuming they exist)
        if "world_points" in batch:
            gt_points = batch["world_points"]
            point_masks = batch.get("point_masks", torch.ones_like(gt_points[..., 0]))
        else:
            # If no ground truth points, use depth-based reconstruction
            gt_points = self._reconstruct_points_from_depth(batch)
            point_masks = batch.get("point_masks", torch.ones_like(gt_points[..., 0]))
        
        # Apply confidence weighting if available
        if "conf" in predictions:
            conf = predictions["conf"].squeeze(-1)  # (B, N, H, W)
            point_masks = point_masks * conf
        
        # Compute scale-invariant point loss
        if self.points.get("scale_invariant", True):
            loss_points = self._scale_invariant_point_loss(
                pred_points, gt_points, point_masks
            )
        else:
            loss_points = self._standard_point_loss(
                pred_points, gt_points, point_masks
            )
        
        # Add gradient-based smoothness loss if specified
        loss_grad = torch.tensor(0.0, device=pred_points.device)
        if self.points.get("gradient_loss_fn") == "normal":
            loss_grad = self._compute_normal_consistency_loss(pred_points, point_masks)
        
        return {
            "loss_points": loss_points + loss_grad * 0.1,
            "loss_points_reconstruction": loss_points,
            "loss_points_smoothness": loss_grad
        }

    def _compute_confidence_loss(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute confidence loss to encourage the model to be confident on accurate predictions
        and uncertain on inaccurate ones.
        """
        pred_conf = predictions["conf"].squeeze(-1)  # (B, N, H, W)
        
        # Compute ground truth confidence based on prediction accuracy
        if "points" in predictions and "world_points" in batch:
            pred_points = predictions["points"]
            gt_points = batch["world_points"]
            
            # Confidence should be high where predictions are accurate
            point_errors = torch.norm(pred_points - gt_points, dim=-1)  # (B, N, H, W)
            threshold = self.confidence.get("confidence_threshold", 0.8)
            gt_conf = (point_errors < threshold).float()
        else:
            # Fallback: use uniform confidence
            gt_conf = torch.ones_like(pred_conf)
        
        # Binary cross-entropy loss for confidence
        loss_confidence = F.binary_cross_entropy_with_logits(
            pred_conf, gt_conf, reduction='mean'
        )
        
        return {"loss_confidence": loss_confidence}

    def _compute_camera_loss(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute camera pose loss using geodesic distance for rotations.
        
        This loss is crucial for accurate camera pose estimation in Pi3's
        reference-view-free framework.
        """
        pred_poses = predictions["camera_poses"]  # (B, N, 4, 4)
        
        if "camera_poses" in batch:
            gt_poses = batch["camera_poses"]
        elif "extrinsics" in batch:
            gt_poses = batch["extrinsics"]
        else:
            # No ground truth poses available
            return {"loss_camera": torch.tensor(0.0, device=pred_poses.device)}
        
        # Extract rotation and translation components
        pred_R = pred_poses[..., :3, :3]  # (B, N, 3, 3)
        pred_t = pred_poses[..., :3, 3]   # (B, N, 3)
        gt_R = gt_poses[..., :3, :3]
        gt_t = gt_poses[..., :3, 3]
        
        # Geodesic loss for rotations
        loss_rotation = self._geodesic_rotation_loss(pred_R, gt_R)
        
        # L2 loss for translations
        loss_translation = F.mse_loss(pred_t, gt_t)
        
        # Weighted combination
        rot_weight = self.camera.get("rotation_weight", 2.0)
        trans_weight = self.camera.get("translation_weight", 1.0)
        
        total_camera_loss = rot_weight * loss_rotation + trans_weight * loss_translation
        
        return {
            "loss_camera": total_camera_loss,
            "loss_camera_rotation": loss_rotation,
            "loss_camera_translation": loss_translation
        }

    def _compute_permutation_consistency_loss(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute permutation consistency loss - a key component for Pi3's equivariance.
        
        This loss ensures that the model produces consistent results regardless of
        the input view ordering, which is fundamental to Pi3's design.
        """
        if not batch.get("permuted_views_available", False):
            return {"loss_permutation_consistency": torch.tensor(0.0, device=predictions["points"].device)}
        
        # Get predictions for original and permuted view orders
        original_points = predictions["points"]
        original_poses = predictions["camera_poses"]
        
        # Apply a random permutation to the views and get predictions
        B, N = original_points.shape[:2]
        perm_indices = torch.randperm(N, device=original_points.device)
        
        # Permute the predictions according to the permutation
        permuted_points = original_points[:, perm_indices]
        permuted_poses = original_poses[:, perm_indices]
        
        # The loss should be small when predictions are consistent under permutation
        # We compare the relative geometry which should be invariant
        consistency_loss = self._compute_relative_geometry_consistency(
            original_points, permuted_points, original_poses, permuted_poses, perm_indices
        )
        
        return {"loss_permutation_consistency": consistency_loss}

    def _compute_scale_invariance_loss(self, predictions: Dict, batch: Dict) -> Dict[str, torch.Tensor]:
        """
        Compute scale invariance loss to ensure the model learns scale-invariant representations.
        
        This is important for Pi3's ability to handle scenes at different scales.
        """
        pred_points = predictions["points"]  # (B, N, H, W, 3)
        pred_local_points = predictions["local_points"]  # (B, N, H, W, 3)
        
        # Apply random scale transformation
        scale_factors = torch.rand(pred_points.shape[0], 1, 1, 1, 1, device=pred_points.device) * 1.5 + 0.5
        scaled_points = pred_points * scale_factors
        scaled_local_points = pred_local_points * scale_factors
        
        # The relative distances should be preserved under scaling
        original_distances = self._compute_pairwise_distances(pred_points)
        scaled_distances = self._compute_pairwise_distances(scaled_points)
        
        # Scale invariance loss
        scale_loss = F.mse_loss(
            scaled_distances / scale_factors.squeeze(),
            original_distances
        )
        
        return {"loss_scale_invariance": scale_loss}

    def _scale_invariant_point_loss(self, pred_points: torch.Tensor, gt_points: torch.Tensor, 
                                   masks: torch.Tensor) -> torch.Tensor:
        """
        Compute scale-invariant point reconstruction loss.
        
        This loss is invariant to global scale changes, which is important for
        Pi3's scale-invariant training.
        """
        # Compute relative distances instead of absolute positions
        B, N, H, W, _ = pred_points.shape
        
        # Sample point pairs for relative distance computation
        num_samples = min(1000, H * W // 4)
        
        # Flatten spatial dimensions
        pred_flat = pred_points.view(B, N, -1, 3)  # (B, N, HW, 3)
        gt_flat = gt_points.view(B, N, -1, 3)
        mask_flat = masks.view(B, N, -1)
        
        # Sample valid points
        valid_indices = torch.where(mask_flat > 0.5)
        if len(valid_indices[0]) < 2:
            return torch.tensor(0.0, device=pred_points.device)
        
        # Randomly sample point pairs
        sample_indices = torch.randperm(len(valid_indices[0]), device=pred_points.device)[:num_samples]
        
        sampled_pred = pred_flat[valid_indices[0][sample_indices], 
                                valid_indices[1][sample_indices], 
                                valid_indices[2][sample_indices]]
        sampled_gt = gt_flat[valid_indices[0][sample_indices], 
                            valid_indices[1][sample_indices], 
                            valid_indices[2][sample_indices]]
        
        # Compute pairwise distances
        pred_distances = torch.cdist(sampled_pred, sampled_pred)
        gt_distances = torch.cdist(sampled_gt, sampled_gt)
        
        # Scale-invariant loss using relative distances
        loss = F.smooth_l1_loss(pred_distances, gt_distances)
        
        return loss

    def _standard_point_loss(self, pred_points: torch.Tensor, gt_points: torch.Tensor, 
                           masks: torch.Tensor) -> torch.Tensor:
        """Compute standard L1/L2 point reconstruction loss."""
        diff = pred_points - gt_points
        if self.points.get("loss_type", "smooth_l1") == "smooth_l1":
            loss = F.smooth_l1_loss(diff[masks > 0.5], torch.zeros_like(diff[masks > 0.5]))
        else:
            loss = F.mse_loss(diff[masks > 0.5], torch.zeros_like(diff[masks > 0.5]))
        return loss

    def _geodesic_rotation_loss(self, pred_R: torch.Tensor, gt_R: torch.Tensor) -> torch.Tensor:
        """
        Compute geodesic distance loss for rotation matrices.
        
        This provides a more principled way to measure rotation errors compared to
        element-wise losses on rotation matrices.
        """
        # Compute relative rotation: R_rel = pred_R @ gt_R^T
        R_rel = torch.bmm(pred_R.view(-1, 3, 3), gt_R.view(-1, 3, 3).transpose(-1, -2))
        
        # Compute trace to get rotation angle
        trace = R_rel.diagonal(dim1=-2, dim2=-1).sum(-1)
        
        # Clamp trace to valid range [-1, 3] to avoid numerical issues
        trace = torch.clamp(trace, -1.0, 3.0)
        
        # Geodesic distance: arccos((trace - 1) / 2)
        cos_angle = (trace - 1) / 2
        cos_angle = torch.clamp(cos_angle, -1.0, 1.0)
        geodesic_distance = torch.acos(cos_angle)
        
        return geodesic_distance.mean()

    def _compute_normal_consistency_loss(self, points: torch.Tensor, masks: torch.Tensor) -> torch.Tensor:
        """
        Compute surface normal consistency loss for geometric smoothness.
        
        This encourages the predicted point clouds to have smooth, consistent surface normals.
        """
        B, N, H, W, _ = points.shape
        
        # Compute surface normals using finite differences
        # Horizontal gradients
        dx = points[:, :, :, 1:] - points[:, :, :, :-1]  # (B, N, H, W-1, 3)
        # Vertical gradients  
        dy = points[:, :, 1:, :] - points[:, :, :-1, :]  # (B, N, H-1, W, 3)
        
        # Pad to match original size
        dx = F.pad(dx, (0, 0, 0, 1), mode='replicate')
        dy = F.pad(dy, (0, 0, 1, 0), mode='replicate')
        
        # Compute normals via cross product
        normals = torch.cross(dx, dy, dim=-1)  # (B, N, H, W, 3)
        normals = F.normalize(normals, p=2, dim=-1, eps=1e-8)
        
        # Compute smoothness loss on normals
        normal_dx = normals[:, :, :, 1:] - normals[:, :, :, :-1]
        normal_dy = normals[:, :, 1:, :] - normals[:, :, :-1, :]
        
        # Apply masks
        mask_dx = masks[:, :, :, 1:] * masks[:, :, :, :-1]
        mask_dy = masks[:, :, 1:, :] * masks[:, :, :-1, :]
        
        loss_dx = (normal_dx.norm(dim=-1) * mask_dx).sum() / (mask_dx.sum() + 1e-8)
        loss_dy = (normal_dy.norm(dim=-1) * mask_dy).sum() / (mask_dy.sum() + 1e-8)
        
        return loss_dx + loss_dy

    def _reconstruct_points_from_depth(self, batch: Dict) -> torch.Tensor:
        """
        Reconstruct 3D points from depth maps and camera intrinsics.

        This is used when ground truth 3D points are not directly available
        but can be computed from depth and camera parameters.
        """
        if "depths" not in batch or "intrinsics" not in batch:
            # Return dummy points if reconstruction is not possible
            B, N = batch["images"].shape[:2]
            H, W = batch["images"].shape[-2:]
            return torch.zeros(B, N, H, W, 3, device=batch["images"].device)

        depths = batch["depths"]  # (B, N, H, W)
        intrinsics = batch["intrinsics"]  # (B, N, 3, 3)

        B, N, H, W = depths.shape

        # Create pixel coordinate grids
        y, x = torch.meshgrid(
            torch.arange(H, device=depths.device),
            torch.arange(W, device=depths.device),
            indexing='ij'
        )

        # Homogeneous pixel coordinates
        pixels = torch.stack([x, y, torch.ones_like(x)], dim=-1).float()  # (H, W, 3)
        pixels = pixels.unsqueeze(0).unsqueeze(0).expand(B, N, -1, -1, -1)  # (B, N, H, W, 3)

        # Unproject to 3D using inverse intrinsics
        inv_intrinsics = torch.inverse(intrinsics)  # (B, N, 3, 3)

        # Reshape for batch matrix multiplication
        pixels_flat = pixels.view(B, N, -1, 3)  # (B, N, HW, 3)

        # Apply inverse intrinsics
        rays = torch.bmm(
            inv_intrinsics.view(B * N, 3, 3),
            pixels_flat.view(B * N, -1, 3).transpose(-1, -2)
        ).transpose(-1, -2)  # (B*N, HW, 3)

        rays = rays.view(B, N, H, W, 3)

        # Scale by depth
        depths_expanded = depths.unsqueeze(-1)  # (B, N, H, W, 1)
        points_3d = rays * depths_expanded

        return points_3d

    def _compute_pairwise_distances(self, points: torch.Tensor) -> torch.Tensor:
        """
        Compute pairwise distances between points for scale invariance loss.

        Args:
            points: (B, N, H, W, 3) tensor of 3D points

        Returns:
            Pairwise distances tensor
        """
        B, N, H, W, _ = points.shape

        # Sample a subset of points to make computation tractable
        num_samples = min(100, H * W // 16)

        # Flatten and sample
        points_flat = points.view(B, N, -1, 3)  # (B, N, HW, 3)

        # Random sampling
        indices = torch.randperm(H * W, device=points.device)[:num_samples]
        sampled_points = points_flat[:, :, indices]  # (B, N, num_samples, 3)

        # Compute pairwise distances within each view
        distances = torch.cdist(sampled_points.view(B * N, num_samples, 3),
                               sampled_points.view(B * N, num_samples, 3))

        return distances.view(B, N, num_samples, num_samples)

    def _compute_relative_geometry_consistency(
        self,
        original_points: torch.Tensor,
        permuted_points: torch.Tensor,
        original_poses: torch.Tensor,
        permuted_poses: torch.Tensor,
        perm_indices: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute consistency loss for relative geometry under view permutation.

        The key insight is that relative geometric relationships between views
        should remain consistent regardless of the ordering of input views.
        """
        B, N = original_points.shape[:2]

        # Compute relative poses between consecutive views
        def compute_relative_poses(poses):
            # poses: (B, N, 4, 4)
            rel_poses = []
            for i in range(N - 1):
                # Relative pose from view i to view i+1
                pose_i = poses[:, i]  # (B, 4, 4)
                pose_j = poses[:, i + 1]  # (B, 4, 4)

                # Relative transformation: T_rel = T_j @ T_i^(-1)
                pose_i_inv = torch.inverse(pose_i)
                rel_pose = torch.bmm(pose_j, pose_i_inv)
                rel_poses.append(rel_pose)

            return torch.stack(rel_poses, dim=1)  # (B, N-1, 4, 4)

        # Compute relative poses for original and permuted sequences
        original_rel_poses = compute_relative_poses(original_poses)
        permuted_rel_poses = compute_relative_poses(permuted_poses)

        # The relative poses should be consistent (up to reordering)
        # For simplicity, we use the Frobenius norm of the difference
        consistency_loss = F.mse_loss(original_rel_poses, permuted_rel_poses)

        return consistency_loss


def check_and_fix_inf_nan(tensor: torch.Tensor, name: str, hard_max: float = 100.0) -> torch.Tensor:
    """
    Check for and fix infinite or NaN values in tensors.

    This is a utility function to ensure numerical stability during training.
    """
    if torch.isnan(tensor).any() or torch.isinf(tensor).any():
        print(f"Warning: {name} contains NaN or Inf values. Replacing with zeros.")
        tensor = torch.where(torch.isnan(tensor) | torch.isinf(tensor),
                           torch.zeros_like(tensor), tensor)

    if hard_max is not None:
        tensor = torch.clamp(tensor, max=hard_max)

    return tensor
