# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

import os
import sys
import logging
import torch
import torch.nn as nn
import torch.distributed as dist
from typing import Dict, Any, List, Optional, Mapping
import time
import gc
import torchvision
from copy import deepcopy

# Add the parent directory to the path to import VGGT training utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '../../vggt/training'))

from trainer import Trainer as VGGTTrainer
from train_utils.general import *
from train_utils.normalization import normalize_camera_extrinsics_and_points_batch


class Pi3Trainer(VGGTTrainer):
    """
    Pi3-specific trainer that extends VGGT's training framework.
    
    This trainer implements the specialized training procedures required for Pi3,
    including permutation-equivariant data processing, scale-invariant training,
    and confidence-based learning.
    
    Key differences from VGGT trainer:
    - Permutation-aware data augmentation
    - Scale-invariant loss computation
    - Confidence-weighted training
    - Support for Pi3's unique architecture
    """
    
    def __init__(self, **kwargs):
        """
        Initialize Pi3 trainer with specialized configurations.
        
        Inherits most functionality from VGGT trainer but adds Pi3-specific
        components and training procedures.
        """
        super().__init__(**kwargs)
        
        # Pi3-specific training flags
        self.permutation_training = getattr(self.model_conf, 'permutation_training', True)
        self.scale_invariant_training = getattr(self.model_conf, 'scale_invariant_training', True)
        self.confidence_training = getattr(self.model_conf, 'confidence_training', True)
        
        logging.info(f"Pi3 Trainer initialized with:")
        logging.info(f"  - Permutation training: {self.permutation_training}")
        logging.info(f"  - Scale invariant training: {self.scale_invariant_training}")
        logging.info(f"  - Confidence training: {self.confidence_training}")

    def _process_batch(self, batch: Mapping) -> Mapping:
        """
        Process batch with Pi3-specific data transformations.
        
        This method extends VGGT's batch processing to include:
        - Permutation-based data augmentation
        - Scale normalization for scale-invariant training
        - Confidence map generation
        
        Args:
            batch: Input batch dictionary
            
        Returns:
            Processed batch dictionary with Pi3-specific additions
        """
        # Apply base VGGT processing first
        batch = super()._process_batch(batch)
        
        # Pi3-specific processing
        batch = self._apply_pi3_data_processing(batch)
        
        return batch

    def _apply_pi3_data_processing(self, batch: Mapping) -> Mapping:
        """
        Apply Pi3-specific data processing transformations.
        
        This includes permutation augmentation, scale normalization,
        and confidence map generation.
        """
        # Apply permutation augmentation during training
        if self.model.training and self.permutation_training:
            batch = self._apply_permutation_augmentation(batch)
        
        # Apply scale normalization for scale-invariant training
        if self.scale_invariant_training:
            batch = self._apply_scale_normalization(batch)
        
        # Generate confidence maps if needed
        if self.confidence_training and "confidence_maps" not in batch:
            batch = self._generate_confidence_maps(batch)
        
        return batch

    def _apply_permutation_augmentation(self, batch: Mapping) -> Mapping:
        """
        Apply permutation-based data augmentation.
        
        This is a key component of Pi3 training that ensures the model
        learns permutation-equivariant representations by randomly
        shuffling the order of input views.
        """
        # Get sequence length
        B, N = batch["images"].shape[:2]
        
        # Generate random permutation for each batch element
        permutations = []
        for b in range(B):
            if torch.rand(1).item() < 0.8:  # 80% chance to apply permutation
                perm = torch.randperm(N)
            else:
                perm = torch.arange(N)  # Keep original order
            permutations.append(perm)
        
        # Apply permutations to relevant batch elements
        tensor_keys = ["images", "extrinsics", "intrinsics", "depths"]
        if "world_points" in batch:
            tensor_keys.append("world_points")
        if "point_masks" in batch:
            tensor_keys.append("point_masks")
        
        for key in tensor_keys:
            if key in batch:
                original_tensor = batch[key]
                permuted_tensors = []
                
                for b in range(B):
                    perm = permutations[b]
                    permuted_tensor = original_tensor[b, perm]
                    permuted_tensors.append(permuted_tensor)
                
                batch[key] = torch.stack(permuted_tensors, dim=0)
        
        # Store permutation information for loss computation
        batch["permutation_indices"] = torch.stack(permutations, dim=0)
        batch["permuted_views_available"] = True
        
        return batch

    def _apply_scale_normalization(self, batch: Mapping) -> Mapping:
        """
        Apply scale normalization for scale-invariant training.
        
        This ensures that the model learns representations that are
        invariant to global scale changes in the scene.
        """
        if "world_points" in batch and "extrinsics" in batch:
            # Compute scene scale based on camera positions
            camera_positions = batch["extrinsics"][..., :3, 3]  # (B, N, 3)
            
            # Compute median distance between cameras as scale reference
            B, N = camera_positions.shape[:2]
            scale_factors = []
            
            for b in range(B):
                positions = camera_positions[b]  # (N, 3)
                if N > 1:
                    # Compute pairwise distances
                    distances = torch.cdist(positions, positions)
                    # Use median non-zero distance as scale
                    non_zero_distances = distances[distances > 1e-6]
                    if len(non_zero_distances) > 0:
                        scale = torch.median(non_zero_distances)
                    else:
                        scale = torch.tensor(1.0, device=positions.device)
                else:
                    scale = torch.tensor(1.0, device=positions.device)
                
                scale_factors.append(scale)
            
            scale_factors = torch.stack(scale_factors, dim=0)  # (B,)
            
            # Normalize world points and camera positions by scale
            batch["world_points"] = batch["world_points"] / scale_factors.view(B, 1, 1, 1, 1)
            batch["extrinsics"][..., :3, 3] = batch["extrinsics"][..., :3, 3] / scale_factors.view(B, 1, 1)
            
            # Store scale factors for potential use in loss computation
            batch["scale_factors"] = scale_factors
        
        return batch

    def _generate_confidence_maps(self, batch: Mapping) -> Mapping:
        """
        Generate confidence maps based on geometric and photometric cues.
        
        These confidence maps help the model learn where to trust its predictions
        and where to be uncertain.
        """
        if "images" not in batch:
            return batch
        
        B, N, C, H, W = batch["images"].shape
        
        # Initialize confidence maps
        confidence_maps = torch.ones(B, N, H, W, device=batch["images"].device)
        
        # Reduce confidence near image boundaries
        boundary_margin = 20
        confidence_maps[:, :, :boundary_margin, :] *= 0.5
        confidence_maps[:, :, -boundary_margin:, :] *= 0.5
        confidence_maps[:, :, :, :boundary_margin] *= 0.5
        confidence_maps[:, :, :, -boundary_margin:] *= 0.5
        
        # Reduce confidence in low-texture regions
        if "point_masks" in batch:
            # Use existing point masks as texture indicators
            texture_confidence = batch["point_masks"].float()
            confidence_maps *= (0.3 + 0.7 * texture_confidence)
        
        batch["confidence_maps"] = confidence_maps
        
        return batch

    def _step(self, batch: Mapping, model: nn.Module, phase: str, loss_meters: dict) -> dict:
        """
        Perform a single forward pass with Pi3-specific processing.
        
        This method extends VGGT's step function to handle Pi3's unique
        output format and loss computation requirements.
        """
        # Forward pass through Pi3 model
        predictions = model(images=batch["images"])
        
        # Pi3 returns different keys than VGGT, so we need to adapt
        pi3_predictions = {
            "points": predictions["points"],           # Global 3D points
            "local_points": predictions["local_points"], # Local 3D coordinates
            "conf": predictions["conf"],               # Confidence scores
            "camera_poses": predictions["camera_poses"] # 4x4 transformation matrices
        }
        
        # Compute loss using Pi3-specific loss function
        loss_dict = self.loss(pi3_predictions, batch)
        
        # Combine predictions and loss for logging
        log_data = {**pi3_predictions, **loss_dict, **batch}
        
        # Update loss meters and log scalars
        self._update_and_log_scalars(log_data, phase, self.steps[phase], loss_meters)
        
        # Log visualizations
        self._log_pi3_visuals(log_data, phase, self.steps[phase])
        
        self.steps[phase] += 1
        return loss_dict

    def _log_pi3_visuals(self, batch: Mapping, phase: str, step: int) -> None:
        """
        Log Pi3-specific visualizations to TensorBoard.
        
        This includes point cloud visualizations, confidence maps,
        and camera pose visualizations.
        """
        if not (
            self.logging_conf.log_visuals
            and (phase in self.logging_conf.log_visual_frequency)
            and self.logging_conf.log_visual_frequency[phase] > 0
            and (step % self.logging_conf.log_visual_frequency[phase] == 0)
            and (self.logging_conf.visuals_keys_to_log is not None)
        ):
            return
        
        if phase not in self.logging_conf.visuals_keys_to_log:
            return
        
        keys_to_log = self.logging_conf.visuals_keys_to_log[phase]["keys_to_log"]
        
        # Create visualizations for Pi3-specific outputs
        visuals_dict = {}
        
        if "local_points_vis" in keys_to_log and "local_points" in batch:
            # Visualize local points as depth maps
            local_points = batch["local_points"]  # (B, N, H, W, 3)
            depth_vis = local_points[..., 2:3]  # Z component
            depth_vis = (depth_vis - depth_vis.min()) / (depth_vis.max() - depth_vis.min() + 1e-8)
            visuals_dict["local_points_vis"] = depth_vis.squeeze(-1)
        
        if "conf_vis" in keys_to_log and "conf" in batch:
            # Visualize confidence maps
            conf_vis = batch["conf"].squeeze(-1)  # (B, N, H, W)
            visuals_dict["conf_vis"] = conf_vis
        
        # Log the visualizations
        for key, visual in visuals_dict.items():
            if visual.dim() >= 3:
                name = f"Visuals/{phase}/{key}"
                visual_grid = torchvision.utils.make_grid(
                    visual[0],  # First batch element
                    nrow=min(4, visual.shape[1]),  # Arrange views in a grid
                    normalize=True
                )
                
                if self.rank == 0:
                    self.tb_writer.log_visuals(
                        name, 
                        visual_grid.cpu().numpy(), 
                        step, 
                        self.logging_conf.video_logging_fps
                    )
