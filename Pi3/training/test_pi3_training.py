#!/usr/bin/env python3
# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

"""
Pi3 Training Test Suite

This module provides comprehensive tests for the Pi3 training pipeline,
including unit tests for individual components and integration tests
for the complete training workflow.

Usage:
    python test_pi3_training.py
    python test_pi3_training.py --test_name test_loss_computation
    python test_pi3_training.py --verbose
"""

import os
import sys
import unittest
import torch
import numpy as np
import tempfile
import shutil
from pathlib import Path
import logging

# Add project paths
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# Import Pi3 components
from loss import Pi3MultitaskLoss
from pi3_trainer import Pi3Trainer
from data.pi3_dataloader import Pi3DynamicTorchDataset
from data.pi3_augmentation import Pi3AugmentationPipeline


class TestPi3Loss(unittest.TestCase):
    """Test cases for Pi3 loss functions."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.batch_size = 2
        self.num_views = 4
        self.height = 64
        self.width = 64
        
        # Create loss function
        self.loss_fn = Pi3MultitaskLoss(
            points={"weight": 1.0, "loss_type": "smooth_l1", "scale_invariant": True},
            confidence={"weight": 0.5, "loss_type": "bce"},
            camera={"weight": 2.0, "loss_type": "geodesic"},
            permutation_consistency={"weight": 0.1, "consistency_type": "pairwise"},
            scale_invariance={"weight": 0.05, "invariance_type": "relative"}
        ).to(self.device)

    def create_dummy_predictions(self):
        """Create dummy predictions for testing."""
        B, N, H, W = self.batch_size, self.num_views, self.height, self.width
        
        predictions = {
            "points": torch.randn(B, N, H, W, 3, device=self.device),
            "local_points": torch.randn(B, N, H, W, 3, device=self.device),
            "conf": torch.sigmoid(torch.randn(B, N, H, W, 1, device=self.device)),
            "camera_poses": torch.eye(4, device=self.device).unsqueeze(0).unsqueeze(0).expand(B, N, -1, -1)
        }
        
        # Add small random perturbations to camera poses
        for b in range(B):
            for n in range(N):
                # Random rotation (small angle)
                angle = torch.randn(1, device=self.device) * 0.1
                axis = torch.randn(3, device=self.device)
                axis = axis / torch.norm(axis)
                
                # Random translation
                translation = torch.randn(3, device=self.device) * 0.5
                predictions["camera_poses"][b, n, :3, 3] = translation
        
        return predictions

    def create_dummy_batch(self):
        """Create dummy batch data for testing."""
        B, N, H, W = self.batch_size, self.num_views, self.height, self.width
        
        batch = {
            "images": torch.rand(B, N, 3, H, W, device=self.device),
            "world_points": torch.randn(B, N, H, W, 3, device=self.device),
            "point_masks": torch.rand(B, N, H, W, device=self.device) > 0.3,
            "camera_poses": torch.eye(4, device=self.device).unsqueeze(0).unsqueeze(0).expand(B, N, -1, -1),
            "extrinsics": torch.eye(4, device=self.device).unsqueeze(0).unsqueeze(0).expand(B, N, -1, -1),
            "intrinsics": torch.eye(3, device=self.device).unsqueeze(0).unsqueeze(0).expand(B, N, -1, -1),
            "permuted_views_available": True
        }
        
        return batch

    def test_loss_computation(self):
        """Test basic loss computation."""
        predictions = self.create_dummy_predictions()
        batch = self.create_dummy_batch()
        
        # Compute loss
        loss_dict = self.loss_fn(predictions, batch)
        
        # Check that loss dictionary contains expected keys
        expected_keys = ["objective", "loss_points", "loss_confidence", "loss_camera"]
        for key in expected_keys:
            self.assertIn(key, loss_dict, f"Missing loss key: {key}")
            self.assertIsInstance(loss_dict[key], torch.Tensor, f"Loss {key} is not a tensor")
            self.assertFalse(torch.isnan(loss_dict[key]), f"Loss {key} contains NaN")
            self.assertFalse(torch.isinf(loss_dict[key]), f"Loss {key} contains Inf")

    def test_scale_invariant_loss(self):
        """Test scale invariant loss computation."""
        predictions = self.create_dummy_predictions()
        batch = self.create_dummy_batch()
        
        # Compute loss for original scale
        loss_dict_1 = self.loss_fn(predictions, batch)
        
        # Scale predictions and batch
        scale_factor = 2.0
        scaled_predictions = {
            "points": predictions["points"] * scale_factor,
            "local_points": predictions["local_points"] * scale_factor,
            "conf": predictions["conf"],
            "camera_poses": predictions["camera_poses"].clone()
        }
        scaled_predictions["camera_poses"][..., :3, 3] *= scale_factor
        
        scaled_batch = batch.copy()
        scaled_batch["world_points"] = batch["world_points"] * scale_factor
        scaled_batch["camera_poses"] = batch["camera_poses"].clone()
        scaled_batch["camera_poses"][..., :3, 3] *= scale_factor
        
        # Compute loss for scaled version
        loss_dict_2 = self.loss_fn(scaled_predictions, scaled_batch)
        
        # Scale-invariant losses should be similar
        self.assertAlmostEqual(
            loss_dict_1["loss_points"].item(),
            loss_dict_2["loss_points"].item(),
            places=2,
            msg="Scale-invariant loss is not actually scale-invariant"
        )

    def test_permutation_consistency(self):
        """Test permutation consistency loss."""
        predictions = self.create_dummy_predictions()
        batch = self.create_dummy_batch()
        
        # Compute loss
        loss_dict = self.loss_fn(predictions, batch)
        
        # Check permutation consistency loss
        if "loss_permutation_consistency" in loss_dict:
            perm_loss = loss_dict["loss_permutation_consistency"]
            self.assertIsInstance(perm_loss, torch.Tensor)
            self.assertFalse(torch.isnan(perm_loss))
            self.assertGreaterEqual(perm_loss.item(), 0.0)

    def test_gradient_flow(self):
        """Test that gradients flow properly through the loss."""
        predictions = self.create_dummy_predictions()
        batch = self.create_dummy_batch()
        
        # Make predictions require gradients
        for key in predictions:
            if isinstance(predictions[key], torch.Tensor):
                predictions[key].requires_grad_(True)
        
        # Compute loss and backward pass
        loss_dict = self.loss_fn(predictions, batch)
        total_loss = loss_dict["objective"]
        total_loss.backward()
        
        # Check that gradients exist
        for key in predictions:
            if isinstance(predictions[key], torch.Tensor) and predictions[key].requires_grad:
                self.assertIsNotNone(predictions[key].grad, f"No gradient for {key}")
                self.assertFalse(torch.isnan(predictions[key].grad).any(), f"NaN gradient for {key}")


class TestPi3DataLoader(unittest.TestCase):
    """Test cases for Pi3 data loading."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Create dummy dataset
        self.dummy_dataset = self.create_dummy_dataset()
        
        # Create data loader configuration
        self.common_config = {
            "img_size": 64,
            "patch_size": 14,
            "debug": True,
            "permutation_augment": True,
            "max_sequence_length": 4,
            "scale_augment": True,
            "confidence_weighting": True
        }

    def create_dummy_dataset(self):
        """Create a dummy dataset for testing."""
        class DummyDataset:
            def __init__(self, num_samples=10):
                self.num_samples = num_samples
            
            def __len__(self):
                return self.num_samples
            
            def __getitem__(self, idx):
                return {
                    "images": torch.rand(4, 3, 64, 64),
                    "extrinsics": torch.eye(4).unsqueeze(0).expand(4, -1, -1),
                    "intrinsics": torch.eye(3).unsqueeze(0).expand(4, -1, -1),
                    "depths": torch.rand(4, 64, 64),
                    "world_points": torch.randn(4, 64, 64, 3),
                    "point_masks": torch.rand(4, 64, 64) > 0.3
                }
        
        return DummyDataset()

    def test_dataloader_creation(self):
        """Test Pi3 data loader creation."""
        try:
            dataloader = Pi3DynamicTorchDataset(
                common_config=self.common_config,
                dataset=self.dummy_dataset,
                num_workers=0,
                max_img_per_gpu=2
            )
            self.assertIsNotNone(dataloader)
        except Exception as e:
            self.fail(f"Failed to create Pi3 data loader: {e}")

    def test_batch_collation(self):
        """Test batch collation functionality."""
        dataloader = Pi3DynamicTorchDataset(
            common_config=self.common_config,
            dataset=self.dummy_dataset,
            num_workers=0,
            max_img_per_gpu=2
        )
        
        # Get a batch
        loader = dataloader.get_loader(epoch=0)
        batch = next(iter(loader))
        
        # Check batch structure
        self.assertIn("images", batch)
        self.assertIn("extrinsics", batch)
        self.assertIn("intrinsics", batch)
        
        # Check batch dimensions
        self.assertEqual(len(batch["images"].shape), 5)  # (B, N, C, H, W)


class TestPi3Augmentation(unittest.TestCase):
    """Test cases for Pi3 augmentation pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            "random_crop": True,
            "crop_probability": 0.7,
            "crop_scale_range": [0.8, 1.0],
            "color_jitter": True,
            "color_jitter_params": {
                "brightness": 0.2,
                "contrast": 0.2,
                "saturation": 0.1,
                "hue": 0.05
            },
            "random_scale": True,
            "scale_range": [0.5, 2.0],
            "scale_probability": 0.6,
            "random_view_permutation": True,
            "permutation_probability": 0.8
        }
        
        self.augmentation = Pi3AugmentationPipeline(self.config)

    def create_dummy_sample(self):
        """Create a dummy sample for testing."""
        return {
            "images": torch.rand(4, 3, 64, 64),
            "extrinsics": torch.eye(4).unsqueeze(0).expand(4, -1, -1),
            "intrinsics": torch.eye(3).unsqueeze(0).expand(4, -1, -1),
            "depths": torch.rand(4, 64, 64),
            "world_points": torch.randn(4, 64, 64, 3),
            "point_masks": torch.rand(4, 64, 64) > 0.3
        }

    def test_augmentation_pipeline(self):
        """Test the complete augmentation pipeline."""
        sample = self.create_dummy_sample()
        original_shape = sample["images"].shape
        
        # Apply augmentation
        augmented_sample = self.augmentation(sample)
        
        # Check that sample structure is preserved
        self.assertEqual(set(sample.keys()), set(augmented_sample.keys()))
        
        # Check that image shape is preserved
        self.assertEqual(augmented_sample["images"].shape, original_shape)

    def test_permutation_augmentation(self):
        """Test view permutation augmentation."""
        sample = self.create_dummy_sample()
        
        # Apply permutation augmentation
        augmented_sample = self.augmentation._apply_view_permutation(sample)
        
        # Check that permutation information is stored
        if "has_view_permutation" in augmented_sample:
            self.assertTrue(augmented_sample["has_view_permutation"])
            self.assertIn("permutation_indices", augmented_sample)


class TestPi3Integration(unittest.TestCase):
    """Integration tests for the complete Pi3 training pipeline."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_training_step(self):
        """Test a single training step."""
        # This test would require a more complex setup
        # For now, we'll just check that the components can be imported
        try:
            from pi3_trainer import Pi3Trainer
            from loss import Pi3MultitaskLoss
            self.assertTrue(True)  # If we get here, imports worked
        except ImportError as e:
            self.fail(f"Failed to import Pi3 components: {e}")


def run_tests(test_name=None, verbose=False):
    """Run the test suite."""
    # Setup logging
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Create test suite
    if test_name:
        # Run specific test
        suite = unittest.TestSuite()
        suite.addTest(unittest.defaultTestLoader.loadTestsFromName(test_name))
    else:
        # Run all tests
        suite = unittest.defaultTestLoader.discover('.', pattern='test_*.py')
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Pi3 Training Test Suite")
    parser.add_argument("--test_name", type=str, help="Specific test to run")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    success = run_tests(args.test_name, args.verbose)
    sys.exit(0 if success else 1)
