#!/usr/bin/env python3
# Copyright (c) 2025 Pi3 Training Implementation
# Licensed under the 2-clause BSD License

"""
Pi3 Training Script

This script provides a complete training pipeline for Pi3 models, implementing
the permutation-equivariant training methodology described in the Pi3 paper.

Key features:
- Permutation-equivariant data loading and augmentation
- Scale-invariant training procedures
- Confidence-based learning
- Multi-GPU distributed training support
- Comprehensive logging and visualization

Usage:
    python train_pi3.py --config config/pi3_default.yaml
    python train_pi3.py --config config/pi3_default.yaml --resume path/to/checkpoint.pt
    python train_pi3.py --config config/pi3_default.yaml --distributed
"""

import os
import sys
import argparse
import logging
import torch
import torch.distributed as dist
from pathlib import Path
import hydra
from omegaconf import DictConfig, OmegaConf
import warnings

# Add project paths
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# Import Pi3 training components
from pi3_trainer import Pi3Trainer
from loss import Pi3MultitaskLoss
from data.pi3_dataloader import Pi3DynamicTorchDataset

# Import VGGT utilities
vggt_training_path = project_root / "vggt" / "training"
sys.path.insert(0, str(vggt_training_path))

from train_utils.distributed import setup_distributed_training
from train_utils.logging import setup_logging
from train_utils.general import set_seeds


def parse_arguments():
    """Parse command line arguments for Pi3 training."""
    parser = argparse.ArgumentParser(
        description="Pi3 Training Script",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        "--config", 
        type=str, 
        default="config/pi3_default.yaml",
        help="Path to the training configuration file"
    )
    
    parser.add_argument(
        "--resume", 
        type=str, 
        default=None,
        help="Path to checkpoint to resume training from"
    )
    
    parser.add_argument(
        "--distributed", 
        action="store_true",
        help="Enable distributed training"
    )
    
    parser.add_argument(
        "--local_rank", 
        type=int, 
        default=0,
        help="Local rank for distributed training"
    )
    
    parser.add_argument(
        "--world_size", 
        type=int, 
        default=1,
        help="World size for distributed training"
    )
    
    parser.add_argument(
        "--master_addr", 
        type=str, 
        default="localhost",
        help="Master address for distributed training"
    )
    
    parser.add_argument(
        "--master_port", 
        type=str, 
        default="29500",
        help="Master port for distributed training"
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true",
        help="Enable debug mode with additional logging"
    )
    
    parser.add_argument(
        "--validate_only", 
        action="store_true",
        help="Run validation only (no training)"
    )
    
    parser.add_argument(
        "--profile", 
        action="store_true",
        help="Enable profiling for performance analysis"
    )
    
    return parser.parse_args()


def setup_environment(args):
    """Setup the training environment and distributed settings."""
    # Set environment variables for distributed training
    if args.distributed:
        os.environ["MASTER_ADDR"] = args.master_addr
        os.environ["MASTER_PORT"] = args.master_port
        os.environ["WORLD_SIZE"] = str(args.world_size)
        os.environ["RANK"] = str(args.local_rank)
        os.environ["LOCAL_RANK"] = str(args.local_rank)
    
    # Set CUDA device
    if torch.cuda.is_available():
        torch.cuda.set_device(args.local_rank)
        device = f"cuda:{args.local_rank}"
    else:
        device = "cpu"
        warnings.warn("CUDA not available, using CPU training (will be very slow)")
    
    # Setup distributed training if requested
    if args.distributed:
        setup_distributed_training(
            backend="nccl" if torch.cuda.is_available() else "gloo",
            init_method=f"tcp://{args.master_addr}:{args.master_port}",
            world_size=args.world_size,
            rank=args.local_rank
        )
    
    return device


def load_and_validate_config(config_path: str, args) -> DictConfig:
    """Load and validate the training configuration."""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    # Load configuration using Hydra
    with hydra.initialize(config_path="../config", version_base=None):
        cfg = hydra.compose(config_name=Path(config_path).stem)
    
    # Override config with command line arguments
    if args.resume:
        cfg.checkpoint.resume_checkpoint_path = args.resume
    
    if args.debug:
        cfg.logging.log_level_primary = "DEBUG"
        cfg.logging.log_freq = 1
        cfg.limit_train_batches = 10
        cfg.limit_val_batches = 5
    
    if args.validate_only:
        cfg.mode = "val"
    
    # Validate critical configuration parameters
    required_keys = ["model", "loss", "optim", "data", "logging", "checkpoint"]
    for key in required_keys:
        if key not in cfg:
            raise ValueError(f"Missing required configuration key: {key}")
    
    return cfg


def create_trainer(cfg: DictConfig, device: str) -> Pi3Trainer:
    """Create and initialize the Pi3 trainer."""
    logging.info("Initializing Pi3 trainer...")
    
    # Create trainer with configuration
    trainer = Pi3Trainer(
        data=cfg.data,
        model=cfg.model,
        logging=cfg.logging,
        checkpoint=cfg.checkpoint,
        max_epochs=cfg.max_epochs,
        mode=cfg.get("mode", "train"),
        device=device,
        seed_value=cfg.seed_value,
        val_epoch_freq=cfg.val_epoch_freq,
        distributed=cfg.distributed,
        cuda=cfg.cuda,
        limit_train_batches=cfg.get("limit_train_batches"),
        limit_val_batches=cfg.get("limit_val_batches"),
        optim=cfg.optim,
        loss=cfg.loss,
        accum_steps=cfg.get("accum_steps", 1),
    )
    
    logging.info("Pi3 trainer initialized successfully")
    return trainer


def main():
    """Main training function."""
    # Parse arguments
    args = parse_arguments()
    
    # Setup environment
    device = setup_environment(args)
    
    # Load configuration
    try:
        cfg = load_and_validate_config(args.config, args)
    except Exception as e:
        logging.error(f"Failed to load configuration: {e}")
        sys.exit(1)
    
    # Setup logging
    log_dir = cfg.logging.log_dir
    os.makedirs(log_dir, exist_ok=True)
    
    setup_logging(
        name="pi3_training",
        output_dir=log_dir,
        rank=args.local_rank,
        log_level_primary=cfg.logging.log_level_primary,
        log_level_secondary=cfg.logging.log_level_secondary,
        all_ranks=cfg.logging.all_ranks
    )
    
    # Log configuration
    logging.info("=" * 80)
    logging.info("Pi3 Training Started")
    logging.info("=" * 80)
    logging.info(f"Configuration:\n{OmegaConf.to_yaml(cfg)}")
    logging.info(f"Device: {device}")
    logging.info(f"Distributed training: {args.distributed}")
    
    # Set random seeds for reproducibility
    set_seeds(cfg.seed_value, cfg.max_epochs, args.local_rank)
    
    try:
        # Create trainer
        trainer = create_trainer(cfg, device)
        
        # Start training or validation
        if args.validate_only:
            logging.info("Starting validation...")
            trainer.run_val()
        else:
            logging.info("Starting training...")
            trainer.run()
        
        logging.info("Training completed successfully!")
        
    except KeyboardInterrupt:
        logging.info("Training interrupted by user")
        sys.exit(0)
    except Exception as e:
        logging.error(f"Training failed with error: {e}")
        import traceback
        logging.error(traceback.format_exc())
        sys.exit(1)
    finally:
        # Cleanup distributed training
        if args.distributed and dist.is_initialized():
            dist.destroy_process_group()


def run_distributed_training():
    """
    Entry point for distributed training using torch.distributed.launch.
    
    Usage:
        python -m torch.distributed.launch --nproc_per_node=4 train_pi3.py --config config/pi3_default.yaml --distributed
    """
    # Get distributed training parameters from environment
    local_rank = int(os.environ.get("LOCAL_RANK", 0))
    world_size = int(os.environ.get("WORLD_SIZE", 1))
    
    # Update sys.argv to include distributed parameters
    if "--local_rank" not in sys.argv:
        sys.argv.extend(["--local_rank", str(local_rank)])
    if "--world_size" not in sys.argv:
        sys.argv.extend(["--world_size", str(world_size)])
    if "--distributed" not in sys.argv:
        sys.argv.append("--distributed")
    
    main()


if __name__ == "__main__":
    # Check if running with torch.distributed.launch
    if "LOCAL_RANK" in os.environ:
        run_distributed_training()
    else:
        main()
